import {  RoomData } from '@/components/page-components/hotel-details/room/AddRoom';
import api from "./auth"

export async function fetchAllRooms(hotelId : string) {
  try {
    const URL = `admin/hotel/${hotelId}/hotelRoom/get`;
    const response = await api.get(URL);
    return Promise.resolve(response.data.result);
  } catch (error) {
    console.log(error);
    return Promise.reject('error');
  }
}

export async function fetchHotelRoom(hotelId: string,roomId:string) {
  try {
    const response = await fetchAllRooms(hotelId);
    const filteredRooms = response.find((room:RoomData) => room.hotelRoomId === roomId);
    
    return filteredRooms;
  } catch (error) {
    return Promise.reject('error');
  }
}

