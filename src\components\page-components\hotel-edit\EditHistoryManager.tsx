import React, { createContext, useContext, useState, useEffect, useRef } from 'react';

interface EditChange {
  oldStartDates?: string[];
  oldEndDates?: string[];
  newStartDates?: string[];
  newEndDates?: string[];
  oldPrices?: {
    roomPrice?: number;
    adultPrice?: number;
    childPrice?: number;
  };
  newPrices?: {
    roomPrice?: number;
    adultPrice?: number;
    childPrice?: number;
  };
  timestamp: number;
}

interface EditHistory {
  [mealPlanId: string]: {
    lastEditTime: number;
    editCount: number;
    isHighlighted: boolean;
    changes: EditChange[];
  };
}

interface EditHistoryContextType {
  editHistory: EditHistory;
  markAsEdited: (mealPlanId: string, change?: EditChange) => void;
  getEditStatus: (mealPlanId: string) => {
    lastEditTime: number;
    editCount: number;
    isHighlighted: boolean;
    editLevel: 'primary' | 'secondary' | 'tertiary' | 'none';
    changes: EditChange[];
  };
  clearHighlight: (mealPlanId: string) => void;
  clearAllHighlights: () => void;
}

const EditHistoryContext = createContext<EditHistoryContextType | undefined>(undefined);

export const useEditHistory = () => {
  const context = useContext(EditHistoryContext);
  if (!context) {
    throw new Error('useEditHistory must be used within an EditHistoryProvider');
  }
  return context;
};

interface EditHistoryProviderProps {
  children: React.ReactNode;
}

export const EditHistoryProvider: React.FC<EditHistoryProviderProps> = ({ children }) => {
  // Use a ref to track if we've already loaded from localStorage
  const isInitialized = useRef(false);
  
  // Initialize state with data from localStorage
  const [editHistory, setEditHistory] = useState<EditHistory>(() => {
    if (typeof window === 'undefined') {
      return {};
    }
    
    try {
      const savedHistory = localStorage.getItem('hotelEditHistory');
      if (savedHistory) {
        const parsed = JSON.parse(savedHistory);
        console.log('Edit history loaded during initialization:', parsed);
        isInitialized.current = true;
        return parsed;
      }
    } catch (error) {
      console.error('Error loading edit history during initialization:', error);
    }
    console.log('No saved edit history found during initialization, starting fresh');
    return {};
  });

  // Ensure data is loaded on mount if not already loaded
  useEffect(() => {
    if (!isInitialized.current && typeof window !== 'undefined') {
      try {
        const savedHistory = localStorage.getItem('hotelEditHistory');
        console.log('Direct localStorage check on mount:', savedHistory);
        if (savedHistory) {
          const parsed = JSON.parse(savedHistory);
          console.log('Edit history loaded on mount:', parsed);
          setEditHistory(parsed);
          isInitialized.current = true;
        }
      } catch (error) {
        console.error('Error loading edit history on mount:', error);
      }
    }
  }, []);

  // Save edit history to localStorage whenever it changes
  useEffect(() => {
    if (isInitialized.current) {
      try {
        localStorage.setItem('hotelEditHistory', JSON.stringify(editHistory));
        console.log('Edit history saved to localStorage:', editHistory);
      } catch (error) {
        console.error('Error saving edit history:', error);
      }
    }
  }, [editHistory]);

  const markAsEdited = (mealPlanId: string, change?: EditChange) => {
    const now = Date.now();
    console.log(`markAsEdited called for ${mealPlanId} at ${now}`, change);
    console.log(`Current editHistory before update:`, editHistory);
    setEditHistory(prev => {
      const newCount = (prev[mealPlanId]?.editCount || 0) + 1;
      const existingChanges = prev[mealPlanId]?.changes || [];
      const newChanges = change ? [...existingChanges, change] : existingChanges;
      
      const newHistory = {
        ...prev,
        [mealPlanId]: {
          lastEditTime: now,
          editCount: newCount,
          isHighlighted: true,
          changes: newChanges,
        }
      };
      console.log(`markAsEdited updating history for ${mealPlanId}:`, newHistory[mealPlanId]);
      console.log(`New editHistory after update:`, newHistory);
      return newHistory;
    });
  };

  const getEditStatus = (mealPlanId: string) => {
    console.log(`getEditStatus called for ${mealPlanId}`);
    console.log(`Current editHistory:`, editHistory);
    console.log(`Available keys:`, Object.keys(editHistory));
    
    const history = editHistory[mealPlanId];
    if (!history) {
      const status = {
        lastEditTime: 0,
        editCount: 0,
        isHighlighted: false,
        editLevel: 'none' as const,
        changes: []
      };
      console.log(`getEditStatus for ${mealPlanId}: no history found, returning:`, status);
      return status;
    }

    // Determine edit level based on edit count
    let editLevel: 'primary' | 'secondary' | 'tertiary' | 'none' = 'none';
    if (history.editCount === 1) editLevel = 'primary';
    else if (history.editCount === 2) editLevel = 'secondary';
    else if (history.editCount >= 3) editLevel = 'tertiary';

    const status = {
      lastEditTime: history.lastEditTime,
      editCount: history.editCount,
      isHighlighted: history.isHighlighted,
      editLevel,
      changes: history.changes || []
    };
    
    console.log(`getEditStatus for ${mealPlanId}:`, status);
    return status;
  };

  const clearHighlight = (mealPlanId: string) => {
    setEditHistory(prev => ({
      ...prev,
      [mealPlanId]: {
        ...prev[mealPlanId],
        isHighlighted: false,
      }
    }));
  };

  const clearAllHighlights = () => {
    setEditHistory(prev => {
      const updated = { ...prev };
      Object.keys(updated).forEach(key => {
        updated[key] = { ...updated[key], isHighlighted: false };
      });
      return updated;
    });
  };

  return (
    <EditHistoryContext.Provider value={{
      editHistory,
      markAsEdited,
      getEditStatus,
      clearHighlight,
      clearAllHighlights,
    }}>
      {children}
    </EditHistoryContext.Provider>
  );
}; 