# Tariff Extraction Backend Service

This backend service provides comprehensive tariff management capabilities including PDF tariff extraction using Mistral OCR and Google Gemini LLM, along with full CRUD operations for tariff management.

## Features

- **PDF Tariff Extraction**: Extract structured tariff data from PDF files using AI
- **Dual Extraction Methods**: LLM-based extraction with fallback pattern matching
- **Tariff Management**: Complete CRUD operations for hotel tariff uploads
- **Health Monitoring**: Service health check endpoint
- **CORS Support**: Cross-origin resource sharing for frontend integration

## Setup Instructions

### 1. Install Dependencies

```bash
cd Backend
pip install -r requirements.txt
```

### 2. Environment Variables

Create a `.env` file in the Backend directory with the following variables:

```env
MISTRAL_API_KEY=your_mistral_api_key_here
GOOGLE_API_KEY=your_google_api_key_here
```

### 3. API Keys Setup

#### Mistral API Key
1. Go to [Mistral AI Console](https://console.mistral.ai/)
2. Create an account or sign in
3. Navigate to API Keys section
4. Create a new API key
5. Copy the key to your `.env` file

#### Google API Key (for Gemini)
1. Go to [Google AI Studio](https://aistudio.google.com/)
2. Create an account or sign in
3. Generate an API key
4. Copy the key to your `.env` file

### 4. Run the Service

```bash
python app.py
```

The service will start on `http://localhost:5000` with debug mode enabled.

## API Endpoints

### Tariff Extraction

#### POST /api/extract-tariff
Extracts tariff data from uploaded PDF file.

**Request:**
- Method: POST
- Content-Type: multipart/form-data
- Body:
  - `file`: PDF file to extract data from
  - `use_llm`: (optional) "true" or "false" - whether to use LLM extraction (default: true)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "Season": "Peak Season",
      "Start Date": "2024-04-15",
      "End Date": "2024-05-09",
      "Room Category": "Deluxe Room",
      "Plan": "MAP",
      "Room Price": 5000,
      "Adult Price": 0,
      "Child Price": 0
    }
  ],
  "count": 1
}
```

### Health Check

#### GET /api/health
Health check endpoint to verify service status.

**Response:**
```json
{
  "success": true,
  "message": "Tariff extraction service is running"
}
```

### Tariff Management

#### GET /admin/hotel/{hotel_id}/tariffs
Get all tariff uploads for a specific hotel.

**Request:**
- Method: GET
- Path Parameter: `hotel_id` - The unique identifier of the hotel

**Response:**
```json
{
  "success": true,
  "result": [
    {
      "tariffId": "uuid-string",
      "hotelId": "hotel-123",
      "roomId": "room-456",
      "filePath": "/path/to/file.pdf",
      "uploadDate": "2024-01-15T10:30:00",
      "status": "approved",
      "priceData": {...},
      "notes": "Approved with minor adjustments",
      "approvalDate": "2024-01-15T11:00:00",
      "approvedBy": "Admin User"
    }
  ]
}
```

#### POST /admin/hotel/tariff
Create a new tariff upload record.

**Request:**
- Method: POST
- Content-Type: application/json
- Body:
```json
{
  "hotelId": "hotel-123",
  "roomId": "room-456",
  "filePath": "/path/to/uploaded/file.pdf"
}
```

**Response:**
```json
{
  "success": true,
  "result": {
    "tariffId": "generated-uuid",
    "hotelId": "hotel-123",
    "roomId": "room-456",
    "filePath": "/path/to/uploaded/file.pdf",
    "uploadDate": "2024-01-15T10:30:00",
    "status": "pending"
  }
}
```

#### PUT /admin/hotel/tariff/{tariff_id}
Update tariff status (approve/reject) and add price data.

**Request:**
- Method: PUT
- Path Parameter: `tariff_id` - The unique identifier of the tariff
- Content-Type: application/json
- Body:
```json
{
  "status": "approved",
  "priceData": {
    "rooms": [...],
    "seasons": [...]
  },
  "notes": "Approved with adjustments"
}
```

**Response:**
```json
{
  "success": true,
  "result": {
    "tariffId": "tariff-uuid",
    "status": "approved",
    "priceData": {...},
    "notes": "Approved with adjustments",
    "approvalDate": "2024-01-15T11:00:00",
    "approvedBy": "Admin User"
  }
}
```

#### DELETE /admin/hotel/tariff/{tariff_id}
Delete a tariff upload record.

**Request:**
- Method: DELETE
- Path Parameter: `tariff_id` - The unique identifier of the tariff

**Response:**
```json
{
  "success": true,
  "result": {
    "success": true
  }
}
```

## Data Formats

The service supports two extraction methods:

### 1. LLM Extraction (use_llm=true)
Uses Google Gemini to extract structured data with columns:
- Room Category
- Plan (meal plan)
- Start Date
- End Date
- Room Price
- Adult Price
- Child Price
- Season

### 2. Fallback Extraction (use_llm=false)
Uses pattern matching with columns:
- Hotel
- Room Category
- Occupancy
- Meal Plan
- Season
- Start Date
- End Date
- Price

## Data Storage

The service currently uses **in-memory storage** for tariff data. This means:
- Data is lost when the service restarts
- Suitable for development and testing
- **Production deployment should replace with a proper database**

## Integration with Frontend

The frontend automatically detects if this service is running and:
- Shows a green status indicator when service is available
- Automatically extracts tariff data when PDFs are uploaded
- Falls back to mock data when service is unavailable
- Provides tariff management interface for hotel administrators

## Troubleshooting

### Service Not Starting
- Check that all dependencies are installed: `pip install -r requirements.txt`
- Verify API keys are correctly set in `.env` file
- Check that port 5000 is not in use by another service
- Ensure Python version compatibility (Python 3.8+ recommended)

### Extraction Errors
- Ensure PDF files are not corrupted or password-protected
- Check API key limits and quotas for Mistral and Google APIs
- Review the console output for detailed error messages
- Verify PDF contains readable text (not just images)

### Tariff Management Issues
- Check that request payloads match the expected JSON format
- Verify hotel_id and tariff_id parameters are valid UUIDs
- Ensure proper Content-Type headers are set for JSON requests

### CORS Issues
- The service includes CORS headers for frontend integration
- If you change the frontend port, update the CORS configuration in `app.py`
- Check browser console for CORS-related errors

## Dependencies

The service uses the following key dependencies:

- **Flask 2.3.3**: Web framework for API endpoints
- **Flask-CORS 4.0.0**: Cross-origin resource sharing support
- **mistralai 1.0.1**: Mistral AI client for OCR functionality
- **langchain-google-genai 1.0.10**: Google Gemini integration
- **pandas 2.1.4**: Data manipulation and analysis
- **python-dotenv 1.0.0**: Environment variable management

## Production Deployment

For production deployment, consider the following:

### Database Integration
- Replace in-memory storage with a proper database (PostgreSQL, MySQL, etc.)
- Implement proper data persistence and backup strategies
- Add database connection pooling and error handling

### Security Enhancements
- Implement authentication and authorization
- Add API rate limiting
- Use HTTPS for secure communication
- Validate and sanitize all inputs
- Store API keys securely (e.g., using environment variables or secret management)

### Performance Optimization
- Add caching for frequently accessed data
- Implement async processing for large PDF files
- Add request/response compression
- Monitor API performance and add logging

### Scalability
- Use a production WSGI server (e.g., Gunicorn, uWSGI)
- Implement load balancing for multiple instances
- Add health checks and monitoring
- Consider containerization with Docker

### Error Handling
- Implement comprehensive error logging
- Add retry mechanisms for external API calls
- Set up alerting for critical failures
- Provide meaningful error messages to clients

## Development

### Project Structure
```
Backend/
├── app.py                 # Main Flask application
├── extract_tariff.py      # PDF extraction logic
├── requirements.txt       # Python dependencies
├── .env                   # Environment variables (create this)
├── output/               # Output directory for processed files
└── README.md             # This documentation
```

### Adding New Features
1. Follow the existing code structure and patterns
2. Add appropriate error handling and logging
3. Update this README with new endpoint documentation
4. Test thoroughly with various PDF formats
5. Consider backward compatibility when modifying existing endpoints