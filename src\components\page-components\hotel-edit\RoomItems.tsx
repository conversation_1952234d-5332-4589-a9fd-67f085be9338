import { useState } from "react";
import Meal<PERSON>lanList from "./MealPlanList";
import { BedDouble, ChevronDown, ChevronUp, Edit, Trash2, Utensils } from "lucide-react";
import { HotelRoom } from "@/types/types";
import { Button } from "@/components/ui/button";
import { Link, useParams } from "react-router-dom";
import { cn } from "@/lib/utils";

interface RoomItemProps {
    roomData: HotelRoom;
    expandAll?: boolean;
}

const RoomItems = ({ roomData, expandAll }: RoomItemProps) => {
    const [showMealPlans, setShowMealPlans] = useState<boolean>(false);
    const { hotelRoomId } = useParams();
    
    // Auto-expand meal plans if this room is selected in the URL or expandAll is true
    const isCurrentRoom = hotelRoomId === roomData.hotelRoomId;
    const shouldExpand = expandAll || isCurrentRoom || showMealPlans;
    
    return (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            {/* Card Header */}
            <div className="p-5 flex justify-between items-center border-b border-gray-100">
                <div className="flex items-center space-x-3">
                    <BedDouble className="h-5 w-5 text-blue-600" />
                    <h3 className="text-lg font-medium text-gray-800">{roomData.hotelRoomType}</h3>
                </div>
                
                <div className="flex items-center space-x-2">
                    <Button 
                        variant="outline" 
                        size="sm"
                        className="text-blue-600 border-blue-200 hover:bg-blue-50"
                    >
                        <Edit size={16} className="mr-1.5" />
                        Edit Room
                    </Button>
                    <Button 
                        variant="outline" 
                        size="sm"
                        className="text-red-600 border-red-200 hover:bg-red-50"
                    >
                        <Trash2 size={16} className="mr-1.5" />
                        Delete
                    </Button>
                </div>
            </div>
            
            {/* Card Body */}
            <div className="p-5">
                {/* Room Details */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                    <div className="flex flex-col">
                        <span className="text-xs text-gray-500 uppercase">Room Type</span>
                        <span className="text-sm font-medium mt-1">{roomData.isAc ? "AC" : "Non-AC"}</span>
                    </div>
                    <div className="flex flex-col">
                        <span className="text-xs text-gray-500 uppercase">Capacity</span>
                        <span className="text-sm font-medium mt-1">{roomData.roomCapacity || 0} persons</span>
                    </div>
                    <div className="flex flex-col">
                        <span className="text-xs text-gray-500 uppercase">Adults</span>
                        <span className="text-sm font-medium mt-1">{roomData.maxAdult || 0}</span>
                    </div>
                    <div className="flex flex-col">
                        <span className="text-xs text-gray-500 uppercase">Children</span>
                        <span className="text-sm font-medium mt-1">{roomData.maxChild || 0}</span>
                    </div>
                </div>
                
                {/* Meal Plans Toggle Button */}
                <div className="mt-4 border-t border-gray-100 pt-4">
                    <Link 
                        to={isCurrentRoom 
                            ? `/hotels/edit/${roomData.hotelId}` 
                            : `/hotels/edit/${roomData.hotelId}/${roomData.hotelRoomId}`
                        }
                    >
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={(e) => {
                                e.preventDefault();
                                setShowMealPlans(!showMealPlans);
                            }}
                            className={cn(
                                "w-full justify-between",
                                isCurrentRoom && "border-blue-200 text-blue-600"
                            )}
                        >
                            <div className="flex items-center">
                                <Utensils size={16} className="mr-2" />
                                <span>Meal Plans</span>
                                {roomData.mealPlan && roomData.mealPlan.length > 0 && (
                                    <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        {roomData.mealPlan.length}
                                    </span>
                                )}
                            </div>
                            {shouldExpand ? (
                                <ChevronUp size={16} />
                            ) : (
                                <ChevronDown size={16} />
                            )}
                        </Button>
                    </Link>
                </div>
                
                {/* Meal Plans List (expanded) */}
                {shouldExpand && (
                    <div className="mt-4 bg-gray-50 p-4 rounded-md">
                        <MealPlanList roomId={roomData.hotelRoomId} mealPlan={roomData.mealPlan} />
                    </div>
                )}
            </div>
        </div>
    );
};

export default RoomItems;
