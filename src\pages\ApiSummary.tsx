const apiSummaryHtml = `
<div class="container">
    <h1>TripXplo API Summary</h1>
    <p>I have analyzed the codebase and identified the following API endpoints used in the TripXplo web application. The base URL for all user-related APIs is <code>https://api.tripxplo.com/v1/api/user/</code>.</p>

    <h3>Authentication</h3>
    <ul>
        <li>
            <strong><code>PUT /auth/login</code></strong>
            <ul>
                <li><strong>Description:</strong> Authenticates a user with their email and password.</li>
                <li><strong>File:</strong> <code>app/utils/api/login.ts</code></li>
            </ul>
        </li>
        <li>
            <strong><code>PUT /auth/google/oauth</code></strong>
            <ul>
                <li><strong>Description:</strong> Verifies a Google OAuth token to sign in or register a user.</li>
                <li><strong>File:</strong> <code>app/utils/api/verifyGoogleSignin.ts</code></li>
            </ul>
        </li>
        <li>
            <strong><code>PUT /auth/refreshToken</code></strong>
            <ul>
                <li><strong>Description:</strong> Refreshes an expired access token using a valid refresh token.</li>
                <li><strong>File:</strong> <code>app/utils/api/auth.ts</code> (used in axios interceptor)</li>
            </ul>
        </li>
    </ul>

    <h3>User Profile</h3>
    <ul>
        <li>
            <strong><code>GET /profile</code></strong>
            <ul>
                <li><strong>Description:</strong> Fetches the profile information of the currently authenticated user.</li>
                <li><strong>File:</strong> <code>app/utils/api/getProfile.ts</code></li>
            </ul>
        </li>
        <li>
            <strong><code>PUT /profile/update</code></strong>
            <ul>
                <li><strong>Description:</strong> Updates the profile information for the currently authenticated user.</li>
                <li><strong>File:</strong> <code>app/utils/api/getProfile.ts</code></li>
            </ul>
        </li>
    </ul>

    <h3>Packages</h3>
    <ul>
        <li>
            <strong><code>GET /package</code></strong>
            <ul>
                <li><strong>Description:</strong> Fetches a list of packages based on various filter criteria like destination, interest, dates, number of people, etc.</li>
                <li><strong>Example:</strong> <code>https://api.tripxplo.com/v1/api/user/package?destinationId=...&interestId=...</code></li>
                <li><strong>File:</strong> <code>app/utils/api/getPackages.ts</code></li>
            </ul>
        </li>
        <li>
            <strong><code>GET /package/{packageId}/getOne</code></strong>
            <ul>
                <li><strong>Description:</strong> Fetches the complete details for a single package by its ID.</li>
                <li><strong>File:</strong> <code>app/hooks/usePackage.ts</code></li>
            </ul>
        </li>
        <li>
            <strong><code>GET /package/interest/get</code></strong>
            <ul>
                <li><strong>Description:</strong> Retrieves a list of available interests (e.g., adventure, romantic) for filtering packages.</li>
                <li><strong>File:</strong> <code>app/actions/get-interest.ts</code></li>
            </ul>
        </li>
        <li>
            <strong><code>GET /package/destination/search</code></strong>
            <ul>
                <li><strong>Description:</strong> Searches for destinations. Can be used with a <code>?search=</code> query parameter for typeahead search.</li>
                <li><strong>File:</strong> <code>app/utils/serverurls.ts</code></li>
            </ul>
        </li>
    </ul>

    <h3>Booking</h3>
    <ul>
        <li>
            <strong><code>POST /package/booking</code></strong>
            <ul>
                <li><strong>Description:</strong> Creates a new booking for a package.</li>
                <li><strong>File:</strong> <code>app/utils/api/createBooking.ts</code></li>
            </ul>
        </li>
        <li>
            <strong><code>GET /package/booking</code></strong>
            <ul>
                <li><strong>Description:</strong> Retrieves a list of all bookings for the authenticated user, with pagination.</li>
                <li><strong>File:</strong> <code>app/utils/api/getAllBookings.ts</code></li>
            </ul>
        </li>
        <li>
            <strong><code>GET /package/booking/{bookingId}/getOne</code></strong>
            <ul>
                <li><strong>Description:</strong> Fetches the detailed status and information of a specific booking by its ID.</li>
                <li><strong>File:</strong> <code>app/utils/api/bookingStatus.ts</code></li>
            </ul>
        </li>
    </ul>

    <h3>Availabilities & Customization</h3>
    <ul>
        <li>
            <strong><code>GET /package/{packageId}/available/get</code></strong>
            <ul>
                <li><strong>Description:</strong> Fetches available hotels for a specific package and destination within that package.</li>
                <li><strong>File:</strong> <code>app/hooks/useAvailableHotels.ts</code></li>
            </ul>
        </li>
        <li>
            <strong><code>GET /package/{packageId}/vehicle/get</code></strong>
            <ul>
                <li><strong>Description:</strong> Fetches available vehicles (cabs) for a specific package.</li>
                <li><strong>File:</strong> <code>app/hooks/useAvailableCab.ts</code></li>
            </ul>
        </li>
        <li>
            <strong><code>GET /package/{packageId}/activity/get</code></strong>
            <ul>
                <li><strong>Description:</strong> Fetches available activities for a specific package and destination.</li>
                <li><strong>File:</strong> <code>app/hooks/useAvailableActivity.ts</code></li>
            </ul>
        </li>
    </ul>

    <h3>Coupons</h3>
    <ul>
        <li>
            <strong><code>POST /package/coupon/check</code></strong>
            <ul>
                <li><strong>Description:</strong> Checks the validity and details of a given coupon code.</li>
                <li><strong>File:</strong> <code>app/utils/api/getCoupon.ts</code></li>
            </ul>
        </li>
    </ul>

    <h3>URL Breakdown</h3>
    <div class="url-breakdown">
        <p>Let's break down a sample <code>getOne</code> package request:</p>
        <p><code>https://api.tripxplo.com/v1/api/user/package/GOMANHO-AP18-4D3N4A/getOne?packageId=GOMANHO-AP18-4D3N4A&amp;startDate=2025-07-26&amp;noAdult=2&amp;noChild=0&amp;noRoomCount=1&amp;noExtraAdult=0</code></p>
        <ul>
            <li><strong>Path:</strong> <code>.../package/{packageId}/getOne</code>
                <ul>
                    <li><code>package</code>: Specifies the package resource.</li>
                    <li><code>GOMANHO-AP18-4D3N4A</code>: This is the <strong>{packageId}</strong>, a unique identifier for the specific package.</li>
                    <li><code>getOne</code>: The action to fetch a single package record.</li>
                </ul>
            </li>
            <li><strong>Query Parameters:</strong> These customize the response, especially for pricing.
                <ul>
                    <li><code>packageId=GOMANHO-AP18-4D3N4A</code>: The unique package ID (redundant, as it's in the path).</li>
                    <li><code>startDate=2025-07-26</code>: The desired travel start date, crucial for pricing.</li>
                    <li><code>noAdult=2</code>: Number of adults.</li>
                    <li><code>noChild=0</code>: Number of children.</li>
                    <li><code>noRoomCount=1</code>: Number of hotel rooms.</li>
                    <li><code>noExtraAdult=0</code>: Number of adults beyond standard room capacity.</li>
                </ul>
            </li>
        </ul>
    </div>

    <p>This list should provide a comprehensive overview for your multi-agent development.</p>
</div>
`;

const styles = `
    body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        line-height: 1.6;
        color: #333;
        background-color: #f4f4f4;
        margin: 0;
        padding: 20px;
    }
    .container {
        max-width: 900px;
        margin: 0 auto;
        background-color: #fff;
        padding: 30px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    h1, h3 {
        color: #1a202c;
        border-bottom: 2px solid #718096;
        padding-bottom: 10px;
        margin-top: 30px;
    }
    h1 {
        font-size: 2.5em;
    }
    h3 {
        font-size: 1.75em;
    }
    p {
        color: #4a5568;
    }
    code {
        background-color: #edf2f7;
        color: #2d3748;
        padding: 3px 6px;
        border-radius: 4px;
        font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
        font-size: 0.9em;
    }
    ul {
        list-style: none;
        padding-left: 0;
    }
    li {
        margin-bottom: 25px;
        padding: 20px;
        border: 1px solid #e2e8f0;
        border-radius: 6px;
        background-color: #fafafa;
    }
    li ul {
        padding-left: 20px;
        margin-top: 15px;
    }
    li ul li {
        padding: 0;
        margin-bottom: 10px;
        border: none;
    }
    strong {
        color: #2d3748;
    }
    .url-breakdown {
        padding: 20px;
        border: 1px solid #e2e8f0;
        border-radius: 6px;
        background-color: #fafafa;
        margin-top: 20px;
    }
    .url-breakdown ul {
        padding-left: 20px;
        margin-top: 15px;
    }
    .url-breakdown ul li {
        padding: 0;
        margin-bottom: 10px;
        border: none;
        background: none;
    }
`;

const ApiSummary = () => {
    return (
        <>
            <style>{styles}</style>
            <div dangerouslySetInnerHTML={{ __html: apiSummaryHtml }} />
        </>
    );
};

export default ApiSummary; 