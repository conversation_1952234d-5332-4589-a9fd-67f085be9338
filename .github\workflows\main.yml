name: admin

# Controls when the action will run.
on:
  # Triggers the workflow on push or pull request events but only for the main branch
  push:
    branches: [main]

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # This workflow contains a single job called "build"
  build:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      - name: Deploy to server using SSH
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          port: 22
          script: |
            # Updated workflow - Fixed directory name and removed PM2
            # Navigate to the correct directory
            cd ~/Tripmilestone-admin-frontend
            
            # Reset any local changes to ensure a clean pull
            git reset --hard origin/main
            
            # Pull latest changes
            git pull origin main
            
            # Set up Node.js environment
            export NVM_DIR=~/.nvm
            source ~/.nvm/nvm.sh      
            
            # Install dependencies
            npm install
            
            # Create environment file if it doesn't exist
            if [ ! -f .env ]; then
              echo "VITE_API_SERVER_URL=https://api.tripxplo.com/v1/api/" > .env
            fi
            
            # Build the project
            npm run build
            
            # Copy built files to web directory
            sudo cp -r dist/* /var/www/admin.tripxplo.com/
            sudo chown -R www-data:www-data /var/www/admin.tripxplo.com/
            sudo chmod -R 755 /var/www/admin.tripxplo.com/
            
            # Restart web server (uncomment the one you're using)
            # sudo systemctl restart apache2
            sudo systemctl restart nginx
            
            echo "Deployment completed successfully!"