import React from 'react';
import { cn } from '@/lib/utils';
import { useEditHistory } from './EditHistoryManager';

interface UpdatedRowHighlightProps {
  children: React.ReactNode;
  mealPlanId: string;
  className?: string;
}

const UpdatedRowHighlight: React.FC<UpdatedRowHighlightProps> = ({ 
  children, 
  mealPlanId,
  className 
}) => {
  const { getEditStatus } = useEditHistory();
  const { isHighlighted, editLevel, editCount, lastEditTime } = getEditStatus(mealPlanId);
  
  // Debug logging
  console.log(`UpdatedRowHighlight rendering for ${mealPlanId}:`, { isHighlighted, editLevel, editCount, lastEditTime });

  // Format the last edit time
  const formatLastEdit = (timestamp: number) => {
    if (timestamp === 0) return '';
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  // Get styling based on edit level
  const getEditLevelStyles = () => {
    switch (editLevel) {
      case 'primary':
        return 'edit-history-primary meal-plan-item-highlighted';
      case 'secondary':
        return 'edit-history-secondary meal-plan-item-highlighted';
      case 'tertiary':
        return 'edit-history-tertiary meal-plan-item-highlighted';
      default:
        return '';
    }
  };

  return (
    <div
      className={cn(
        'transition-all duration-500 ease-in-out relative',
        isHighlighted && getEditLevelStyles(),
        className
      )}
    >
      {/* Edit count badge */}
      {editCount > 0 && (
        <div className="absolute -top-2 -right-2 z-10">
          <div className={cn(
            'flex items-center justify-center w-6 h-6 rounded-full text-xs font-bold text-white shadow-md edit-count-badge',
            editLevel === 'primary' && 'bg-blue-500',
            editLevel === 'secondary' && 'bg-purple-500',
            editLevel === 'tertiary' && 'bg-orange-500'
          )}>
            {editCount}
          </div>
        </div>
      )}
      
      {/* Last edit time tooltip */}
      {isHighlighted && lastEditTime > 0 && (
        <div className="absolute -top-8 left-0 edit-history-tooltip text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
          Last edited: {formatLastEdit(lastEditTime)}
        </div>
      )}
      
      {children}
    </div>
  );
};

export default UpdatedRowHighlight; 