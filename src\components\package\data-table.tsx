import React from "react"
import {
    ColumnDef,
    flexRender,
    getCoreRowModel,
    getPaginationRowModel,
    ColumnFiltersState,
    getFilteredRowModel,
    useReactTable,
  } from "@tanstack/react-table"
  import { DropdownMenu, DropdownMenuCheckboxItem, DropdownMenuContent, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
  
  import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
  } from "@/components/ui/table"
  
  import { Input } from "@/components/ui/input"
  import { ChevronLeft, ChevronRight, ListFilter } from "lucide-react"
import { Button } from "../ui/button"
  
  interface DataTableProps<TData, TValue> {
    columns: ColumnDef<TData, TValue>[]
    data: TData[]
    noOfPackages : number
  }
  
  export function DataTable<TData, TValue>({
    columns,
    data,
    noOfPackages 
  
  }: DataTableProps<TData, TValue>) {
    const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  
    const table = useReactTable({
      data,
      columns,
      getCoreRowModel: getCoreRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      onColumnFiltersChange: setColumnFilters,
      getFilteredRowModel: getFilteredRowModel(),
      state: {
        columnFilters,
      },
    })
  
    const totalPages = table.getPageCount()
    const currentPage = table.getState().pagination.pageIndex + 1
  
    return (
      <div className="">
   
        <div className="flex items-center justify-between py-4 ">
          <div className="flex items-center  gap-10">
          <div className="">
       <h1 className="font-bold text-xl ">
  <span className="bg-appprimary text-white p-2 rounded-full">
  {noOfPackages}</span> <span className="font-medium text-neutral-700">Activities</span>
       </h1>
        
  
      </div>
      <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="ml-auto">
             <h1 className="text-lg text-neutral-600">
             <ListFilter size={18}/> </h1>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {table
              .getAllColumns()
              .filter((column) => column.getCanHide())
              .map((column) => {
                return (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    className="capitalize"
                    checked={column.getIsVisible()}
                    onCheckedChange={(value) =>
                      column.toggleVisibility(!!value)
                    }
                  >
                    {column.id}
                  </DropdownMenuCheckboxItem>
                )
              })}
          </DropdownMenuContent>
        </DropdownMenu>
          <Input
            placeholder="Search an Activity . . ."
            value={(table.getColumn("hotelName")?.getFilterValue() as string) ?? ""}
            onChange={(event) =>
              table.getColumn("hotelName")?.setFilterValue(event.target.value)
            }
            className="w-96 focus:outline-none active:outline-none outline-none"
          />
       
          </div>
        
          <a href="/activity/add" className="bg-appprimary text-white px-4 py-3 rounded-md">
          Create an Activity
          </a>
        </div>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <TableHead key={header.id}>
                      {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow key={row.id} data-state={row.getIsSelected() && "selected"}>
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    No results.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
        <div className="fixed ml-[16.7%] bottom-0 bg-white  z-50 left-0 w-full  z-50 p-8 border-t shadow-lg">
    <div className="flex justify-center items-center max-w-screen-xl mx-auto px-4">
      <button
       
        
        onClick={() => table.previousPage()}
        disabled={!table.getCanPreviousPage()}
          className="bottom-3 left-[18%] fixed border border-appprimary text-appprimary rounded-md p-2"
      >
        <ChevronLeft />
     
      </button>
      <span className="fixed bottom-3 left-[57%] transform  -translate-x-1/2">
        Page <span className="underline underline-offset-4">{currentPage} of {totalPages}</span>
      </span>
      <button
       
        
        onClick={() => table.nextPage()}
        disabled={!table.getCanNextPage()}
        className="fixed bottom-3 right-3 border border-appprimary rounded-md p-2  text-appprimary"
      >
        <ChevronRight/>
      </button>
    </div>
  </div>
  
      </div>
    )
  }
  