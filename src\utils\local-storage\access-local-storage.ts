/* eslint-disable @typescript-eslint/no-explicit-any */
export function getAccessToken() {
    return localStorage.getItem('accessToken');
  }
  export function getRefreshToken() {
    return localStorage.getItem('refreshToken');
  }

  export function setAccessToken(data: any) {
    localStorage.setItem('accessToken', data);
  }

  export function setRefreshToken(data: any) {
    localStorage.setItem('refreshToken', data);
  }

  export function cleanToken() {
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
  }

// Generic localStorage utility functions
export function setLocalStorageItem(key: string, value: any): void {
  try {
    localStorage.setItem(key, JSON.stringify(value));
  } catch (error) {
    console.error(`Error setting localStorage item ${key}:`, error);
  }
}

export function getLocalStorageItem<T>(key: string): T | null {
  try {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : null;
  } catch (error) {
    console.error(`Error getting localStorage item ${key}:`, error);
    return null;
  }
}

export function removeLocalStorageItem(key: string): void {
  try {
    localStorage.removeItem(key);
  } catch (error) {
    console.error(`Error removing localStorage item ${key}:`, error);
  }
}
  