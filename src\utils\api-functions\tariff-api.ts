import axios from 'axios';
import { getAccessToken } from '../local-storage/access-local-storage';

// Create a separate API client for tariff operations that points to the local Flask server
const tariffApi = axios.create({
  baseURL: 'http://localhost:5000',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to include auth token
tariffApi.interceptors.request.use(
  (config) => {
    const token = getAccessToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Fix double slashes in URL paths
    if (config.url) {
      // Remove leading slash if present to avoid double slashes
      if (config.url.startsWith('/')) {
        config.url = config.url.substring(1);
      }
    }

    // Debug log the URL
    console.log('Tariff API URL:', `${config.baseURL}/${config.url}`);

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

export default tariffApi;
