import React, { useState, useMemo } from "react"
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  ColumnFiltersState,
  getFilteredRowModel,
  useReactTable,
} from "@tanstack/react-table"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Input } from "@/components/ui/input"
import { ListFilter, Search, Download } from "lucide-react"
import { DropdownMenu, DropdownMenuCheckboxItem, DropdownMenuContent, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import * as XLSX from 'xlsx';
import Select from 'react-select';
import makeAnimated from 'react-select/animated';

const animatedComponents = makeAnimated();

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  data: TData[]
  onCountChange: (count: number) => void
}

export function DataTable<TData, TValue>({
  columns,
  data,
  onCountChange,
}: DataTableProps<TData, TValue>) {
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [pageSize, setPageSize] = useState(25)
  const [pageIndex, setPageIndex] = useState(0)
  const [locationFilter, setLocationFilter] = useState<string[]>([]);
  const [emailFilter, setEmailFilter] = useState("");

  // Create a list of unique locations for the dropdown filter
  const uniqueLocations = useMemo(() => {
    const locations = new Set<string>();
    data.forEach((hotel: any) => {
      if (hotel.location?.state) {
        locations.add(hotel.location.state);
      }
    });
    return Array.from(locations).sort().map(location => ({ value: location, label: location }));
  }, [data]);

  // Reset to first page when filters change
  React.useEffect(() => {
    setPageIndex(0)
  }, [columnFilters])

  // Memoize the filtering logic to prevent re-calculation on every render
  const filteredData = useMemo(() => {
    return data.filter((hotel: any) => {
      const locationMatch = locationFilter.length > 0
        ? locationFilter.includes(hotel.location?.state)
        : true;
      const emailMatch = emailFilter
        ? hotel.contract?.businessEmail?.toLowerCase().includes(emailFilter.toLowerCase())
        : true;
      return locationMatch && emailMatch;
    });
  }, [data, locationFilter, emailFilter]);

  const table = useReactTable({
    data: filteredData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    state: {
      columnFilters,
      pagination: { pageIndex, pageSize },
    },
    onPaginationChange: (updater) => {
      if (typeof updater === 'function') {
        const next = updater({ pageIndex, pageSize });
        setPageIndex(next.pageIndex);
        setPageSize(next.pageSize);
      } else {
        setPageIndex(updater.pageIndex);
        setPageSize(updater.pageSize);
      }
    },
    manualPagination: false,
    pageCount: Math.ceil(filteredData.length / pageSize),
  });

  // Update the parent component's count when the filtered data changes
  React.useEffect(() => {
    onCountChange(table.getFilteredRowModel().rows.length);
  }, [table.getFilteredRowModel().rows.length, onCountChange]);

  // Get the filtered rows count for pagination
  const filteredRowsCount = table.getFilteredRowModel().rows.length

  // Calculate total pages based on filtered rows when search is active
  // or total rows when no search is applied
  const hasActiveFilters = columnFilters.length > 0 || locationFilter.length > 0 || emailFilter.length > 0;
  const totalPages = hasActiveFilters
    ? Math.ceil(filteredRowsCount / pageSize)
    : table.getPageCount()

  // Ensure we don't stay on a non-existent page after filtering
  React.useEffect(() => {
    if (pageIndex >= totalPages && totalPages > 0) {
      setPageIndex(totalPages - 1);
    }
  }, [totalPages, pageIndex]);

  // Excel export handler
  const handleDownloadExcel = () => {
    // Get the filtered and sorted rows from the table model
    const rowsToExport = table.getFilteredRowModel().rows;

    // Prepare data for export
    const exportData = rowsToExport.map(row => {
      const hotel = row.original as any; // Use 'as any' or a more specific type if available
      return {
        'Hotel Name': hotel.hotelName,
        'Location': `${hotel.location?.state || ''}, ${hotel.location?.country || ''}`,
        'Rooms': hotel.roomCount ?? 0,
        'Contact Email': hotel.contract?.businessEmail || '',
      };
    });

    if (exportData.length === 0) {
      alert("No data to export!");
      return;
    }

    const worksheet = XLSX.utils.json_to_sheet(exportData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Hotels');
    XLSX.writeFile(workbook, 'filtered-hotels-list.xlsx');
  };

  return (
    <div className="w-full">
      {/* Filters and Actions Bar */}
      <div className="flex items-center justify-between gap-4 py-4">
        {/* Filter Inputs */}
        <div className="flex items-center gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search by name..."
              value={(table.getColumn("hotelName")?.getFilterValue() as string) ?? ""}
              onChange={(event) =>
                table.getColumn("hotelName")?.setFilterValue(event.target.value)
              }
              className="w-64 pl-10"
            />
          </div>

          <Select
            isMulti
            components={animatedComponents}
            options={uniqueLocations}
            className="w-64"
            classNamePrefix="select"
            placeholder="Filter by location..."
            onChange={(selectedOptions) => {
              setLocationFilter(selectedOptions ? selectedOptions.map(option => (option as { value: string }).value) : []);
            }}
          />

          <Input
            placeholder="Filter by email..."
            value={emailFilter}
            onChange={e => setEmailFilter(e.target.value)}
            className="w-64"
          />
        </div>

        {/* Action Buttons */}
        <div className="flex items-center gap-4">
          <Button onClick={handleDownloadExcel} variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export Excel
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="ml-auto">
                <ListFilter className="mr-2 h-4 w-4" />
                View
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) =>
                        column.toggleVisibility(!!value)
                      }
                    >
                      {column.id}
                    </DropdownMenuCheckboxItem>
                  )
                })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      
      {/* Table Section */}
      <div className="rounded-md border mt-4">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow key={row.id} data-state={row.getIsSelected() && "selected"}>
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Footer with Pagination and Row Count */}
      <div className="flex items-center justify-between mt-4">
        <div className="text-sm text-muted-foreground">
          Showing{" "}
          <strong>
            {table.getRowModel().rows.length > 0
              ? table.getState().pagination.pageIndex * table.getState().pagination.pageSize + 1
              : 0}
            {" - "}
            {table.getState().pagination.pageIndex * table.getState().pagination.pageSize +
              table.getRowModel().rows.length}
          </strong>{" "}
          of <strong>{filteredData.length}</strong> hotels.
        </div>
        <div className="flex items-center gap-4">
          {/* Pagination controls */}
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.setPageIndex(0)}
              disabled={!table.getCanPreviousPage()}
            >
              {'<<'}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              {'<'}
            </Button>
            {/* Page buttons can be added here if needed */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              {'>'}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.setPageIndex(table.getPageCount() - 1)}
              disabled={!table.getCanNextPage()}
            >
              {'>>'}
            </Button>
          </div>
          {/* Page size selector */}
          <div className="flex items-center gap-2">
            <span className="text-sm">Rows per page:</span>
            <select
              value={table.getState().pagination.pageSize}
              onChange={e => {
                table.setPageSize(Number(e.target.value))
              }}
              className="p-2 border rounded-md bg-white"
            >
              {[25, 50, 100, 200].map(pageSize => (
                <option key={pageSize} value={pageSize}>
                  {pageSize}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>
    </div>
  );
}
