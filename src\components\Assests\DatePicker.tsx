import { FC, useState } from 'react';
import { DateRange, Range } from 'react-date-range';
import 'react-date-range/dist/styles.css';
import 'react-date-range/dist/theme/default.css';

interface DatePickerProps {
  onDateChange: (range: [Date | null, Date | null]) => void;
}

const DatePickerComponent: FC<DatePickerProps> = ({ onDateChange }) => {
  const [selection, setSelection] = useState<Range[]>([
    {
      startDate: new Date(),
      endDate: new Date(),
      key: 'selection',
    },
  ]);

  const handleSelect = (ranges: any) => {
    setSelection([ranges.selection]);
    const range = ranges.selection;
    onDateChange([range.startDate || null, range.endDate || null]);
  };

  return (
    <DateRange
      ranges={selection}
      onChange={handleSelect}
      rangeColors={['#3d91ff']}
      moveRangeOnFirstSelection={false}
      months={1}
      direction="horizontal"
    />
  );
};

export default DatePickerComponent;
