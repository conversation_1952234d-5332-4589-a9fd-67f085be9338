import { ExtractedTariffData } from '@/types/types';
import { setLocalStorageItem, getLocalStorageItem, removeLocalStorageItem } from '@/utils/local-storage/access-local-storage';

// Cache configuration
const CACHE_DURATION_MS = 30 * 60 * 1000; // 30 minutes in milliseconds
const TARIFF_CACHE_KEY = 'tariff_cache_data';
const CACHE_METADATA_KEY = 'tariff_cache_metadata';

// Interface for cached tariff data
export interface CachedTariffData {
  data: ExtractedTariffData[];
  timestamp: number;
  hotelId: string;
  fileName: string;
  fileSize: number;
  fileLastModified: number;
}

// Interface for cache metadata
export interface TariffCacheMetadata {
  lastUpdated: number;
  hotelId: string;
  fileName: string;
  expiresAt: number;
  isValid: boolean;
}

/**
 * Cache tariff data with metadata for 30-minute persistence
 */
export function cacheTariffData(
  data: ExtractedTariffData[],
  hotelId: string,
  file: File
): void {
  try {
    const timestamp = Date.now();
    const expiresAt = timestamp + CACHE_DURATION_MS;

    const cachedData: CachedTariffData = {
      data,
      timestamp,
      hotelId,
      fileName: file.name,
      fileSize: file.size,
      fileLastModified: file.lastModified
    };

    const metadata: TariffCacheMetadata = {
      lastUpdated: timestamp,
      hotelId,
      fileName: file.name,
      expiresAt,
      isValid: true
    };

    // Store both data and metadata
    setLocalStorageItem(TARIFF_CACHE_KEY, cachedData);
    setLocalStorageItem(CACHE_METADATA_KEY, metadata);

    console.log(`Tariff data cached for hotel ${hotelId}, expires at:`, new Date(expiresAt));
  } catch (error) {
    console.error('Error caching tariff data:', error);
  }
}

/**
 * Retrieve cached tariff data if valid and not expired
 */
export function getCachedTariffData(hotelId?: string): ExtractedTariffData[] | null {
  try {
    const metadata = getLocalStorageItem<TariffCacheMetadata>(CACHE_METADATA_KEY);
    const cachedData = getLocalStorageItem<CachedTariffData>(TARIFF_CACHE_KEY);

    // Check if cache exists
    if (!metadata || !cachedData) {
      console.log('No cached tariff data found');
      return null;
    }

    // Check if cache is expired
    const now = Date.now();
    if (now > metadata.expiresAt) {
      console.log('Cached tariff data expired, clearing cache');
      clearTariffCache();
      return null;
    }

    // Check if hotel ID matches (if provided)
    if (hotelId && cachedData.hotelId !== hotelId) {
      console.log('Cached data is for different hotel, ignoring cache');
      return null;
    }

    // Check if cache is marked as invalid
    if (!metadata.isValid) {
      console.log('Cached tariff data marked as invalid');
      return null;
    }

    console.log(`Retrieved cached tariff data for hotel ${cachedData.hotelId}, ${cachedData.data.length} records`);
    return cachedData.data;
  } catch (error) {
    console.error('Error retrieving cached tariff data:', error);
    return null;
  }
}

/**
 * Check if cached tariff data exists and is valid
 */
export function hasCachedTariffData(hotelId?: string): boolean {
  const data = getCachedTariffData(hotelId);
  return data !== null && data.length > 0;
}

/**
 * Get cache metadata information
 */
export function getTariffCacheInfo(): TariffCacheMetadata | null {
  try {
    const metadata = getLocalStorageItem<TariffCacheMetadata>(CACHE_METADATA_KEY);
    if (!metadata) {
      return null;
    }

    // Update validity based on expiration
    const now = Date.now();
    const isValid = now <= metadata.expiresAt && metadata.isValid;

    return {
      ...metadata,
      isValid
    };
  } catch (error) {
    console.error('Error getting cache info:', error);
    return null;
  }
}

/**
 * Get remaining cache time in milliseconds
 */
export function getCacheRemainingTime(): number {
  const metadata = getTariffCacheInfo();
  if (!metadata || !metadata.isValid) {
    return 0;
  }

  const now = Date.now();
  const remaining = metadata.expiresAt - now;
  return Math.max(0, remaining);
}

/**
 * Get remaining cache time in human-readable format
 */
export function getCacheRemainingTimeFormatted(): string {
  const remainingMs = getCacheRemainingTime();
  if (remainingMs === 0) {
    return 'Expired';
  }

  const minutes = Math.floor(remainingMs / (1000 * 60));
  const seconds = Math.floor((remainingMs % (1000 * 60)) / 1000);

  if (minutes > 0) {
    return `${minutes}m ${seconds}s`;
  }
  return `${seconds}s`;
}

/**
 * Clear all cached tariff data
 */
export function clearTariffCache(): void {
  try {
    removeLocalStorageItem(TARIFF_CACHE_KEY);
    removeLocalStorageItem(CACHE_METADATA_KEY);
    console.log('Tariff cache cleared');
  } catch (error) {
    console.error('Error clearing tariff cache:', error);
  }
}

/**
 * Invalidate cache without removing it (mark as invalid)
 */
export function invalidateTariffCache(): void {
  try {
    const metadata = getLocalStorageItem<TariffCacheMetadata>(CACHE_METADATA_KEY);
    if (metadata) {
      const updatedMetadata: TariffCacheMetadata = {
        ...metadata,
        isValid: false
      };
      setLocalStorageItem(CACHE_METADATA_KEY, updatedMetadata);
      console.log('Tariff cache invalidated');
    }
  } catch (error) {
    console.error('Error invalidating tariff cache:', error);
  }
}

/**
 * Check if the cached data is for the same file
 */
export function isSameFile(file: File, hotelId: string): boolean {
  try {
    const cachedData = getLocalStorageItem<CachedTariffData>(TARIFF_CACHE_KEY);
    if (!cachedData) {
      return false;
    }

    return (
      cachedData.hotelId === hotelId &&
      cachedData.fileName === file.name &&
      cachedData.fileSize === file.size &&
      cachedData.fileLastModified === file.lastModified
    );
  } catch (error) {
    console.error('Error checking file similarity:', error);
    return false;
  }
}

/**
 * Update cached data with new information (extends cache life)
 */
export function updateCachedTariffData(
  data: ExtractedTariffData[],
  hotelId: string,
  file: File
): void {
  // Simply cache new data, which will reset the expiration time
  cacheTariffData(data, hotelId, file);
}

/**
 * Get cache statistics for debugging
 */
export function getCacheStats(): {
  hasCache: boolean;
  isValid: boolean;
  isExpired: boolean;
  recordCount: number;
  remainingTime: string;
  hotelId?: string;
  fileName?: string;
} {
  const metadata = getTariffCacheInfo();
  const cachedData = getLocalStorageItem<CachedTariffData>(TARIFF_CACHE_KEY);

  return {
    hasCache: !!metadata && !!cachedData,
    isValid: metadata?.isValid ?? false,
    isExpired: metadata ? Date.now() > metadata.expiresAt : true,
    recordCount: cachedData?.data?.length ?? 0,
    remainingTime: getCacheRemainingTimeFormatted(),
    hotelId: cachedData?.hotelId,
    fileName: cachedData?.fileName
  };
}
