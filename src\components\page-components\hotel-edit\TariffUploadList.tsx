import React, { useState, useEffect } from 'react';
import { useQuery, useQueryClient } from 'react-query';
import { TariffUpload, HotelRoom, ExtractedTariffData } from '@/types/types';
import { Button } from '@/components/ui/button';
import { AlertCircle, Check, FileText, Info, Upload, X, RefreshCw } from 'lucide-react';
import { format } from 'date-fns';
import {
  fetchHotelTariffs,
  updateTariffStatus,
  deleteTariffUpload
} from '@/utils/api-functions/tariff-upload';
import {
  extractTariffFromPDF,
  checkTariffExtractionService
} from '@/utils/api-functions/tariff-extraction';
import { filterRoomsByName } from '@/utils/roomNameMatcher';
// Removed unused import
import toast from 'react-hot-toast';
import {
  cacheTariffData,
  getCachedTariffData,
  getTariffCacheInfo,
  getCacheRemainingTimeFormatted,
  clearTariffCache,
  isSameFile,
} from '@/utils/cache/tariff-cache';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import TariffComparison from './TariffComparison';

// Custom Alert Components since we don't have the imported ones
type AlertVariant = 'default' | 'destructive';

interface AlertProps {
  children: React.ReactNode;
  className?: string;
  variant?: AlertVariant;
}

interface AlertTitleProps {
  children: React.ReactNode;
  className?: string;
}

interface AlertDescriptionProps {
  children: React.ReactNode;
  className?: string;
}

const Alert: React.FC<AlertProps> = ({ children, className = "", variant = "default" }) => {
  const baseClasses = "relative w-full rounded-lg border p-4 mb-4";
  const variantClasses: Record<AlertVariant, string> = {
    default: "bg-white border-gray-200",
    destructive: "bg-red-50 border-red-200 text-red-700",
  };

  return (
    <div
      role="alert"
      className={`${baseClasses} ${variantClasses[variant]} ${className}`}
    >
      {children}
    </div>
  );
};

const AlertTitle: React.FC<AlertTitleProps> = ({ children, className = "" }) => (
  <h5 className={`mb-1 font-medium ${className}`}>{children}</h5>
);

const AlertDescription: React.FC<AlertDescriptionProps> = ({ children, className = "" }) => (
  <div className={`text-sm ${className}`}>{children}</div>
);

interface TariffUploadListProps {
  hotelId: string;
  rooms: HotelRoom[];
}

const TariffUploadList: React.FC<TariffUploadListProps> = ({ hotelId, rooms }) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [selectedRoom, setSelectedRoom] = useState<string>('');
  const [tariffDetailsOpen, setTariffDetailsOpen] = useState(false);
  const [selectedTariff, setSelectedTariff] = useState<TariffUpload | null>(null);
  const [comparisonOpen, setComparisonOpen] = useState(false);
  const [tariffForComparison, setTariffForComparison] = useState<TariffUpload | null>(null);
  const [extractionServiceAvailable, setExtractionServiceAvailable] = useState<boolean>(false);

  // New state for the updated workflow with caching
  const [cachedExtractedData, setCachedExtractedData] = useState<ExtractedTariffData[]>([]);
  const [isExtracting, setIsExtracting] = useState(false);
  const [hasExtractedData, setHasExtractedData] = useState(false);
  const [cacheInfo, setCacheInfo] = useState<any>(null);

  const queryClient = useQueryClient();

  // Fetch tariff uploads with better error handling
  const {
    data: tariffs = [],
    isLoading,
    isError
  } = useQuery<TariffUpload[]>(
    ['tariffs', hotelId],
    () => fetchHotelTariffs(hotelId),
    {
      enabled: !!hotelId,
      retry: 1, // Only retry once to avoid excessive error logs
      onError: (err: any) => {
        console.error('Failed to fetch tariffs:', err);
      }
    }
  );

  // Set the first room as selected by default when rooms are loaded
  useEffect(() => {
    if (rooms?.length > 0 && !selectedRoom) {
      setSelectedRoom(rooms[0].hotelRoomId);
    }
  }, [rooms, selectedRoom]);

  // Clear cached data when hotel changes
  useEffect(() => {
    setCachedExtractedData([]);
    setHasExtractedData(false);
    setSelectedFile(null);
    setSelectedRoom('');
  }, [hotelId]);

  // Auto-clear cache after 30 minutes of inactivity
  useEffect(() => {
    if (hasExtractedData) {
      const timeout = setTimeout(() => {
        setCachedExtractedData([]);
        setHasExtractedData(false);
        toast('Cached PDF data has been automatically cleared due to inactivity', {
          icon: 'ℹ️',
          duration: 4000
        });
      }, 30 * 60 * 1000); // 30 minutes

      return () => clearTimeout(timeout);
    }
  }, [hasExtractedData]);

  // Check if tariff extraction service is available
  useEffect(() => {
    const checkService = async () => {
      const isAvailable = await checkTariffExtractionService();
      setExtractionServiceAvailable(isAvailable);
    };

    checkService();
  }, []);

  // Initialize cache data on component mount
  useEffect(() => {
    const initializeCache = () => {
      // Check if we have cached data for this hotel
      const cachedData = getCachedTariffData(hotelId);
      const cacheMetadata = getTariffCacheInfo();

      if (cachedData && cachedData.length > 0) {
        setCachedExtractedData(cachedData);
        setHasExtractedData(true);
        setCacheInfo(cacheMetadata);
        console.log(`Loaded ${cachedData.length} cached tariff records for hotel ${hotelId}`);
        toast.success(`Loaded cached tariff data (${getCacheRemainingTimeFormatted()} remaining)`);
      } else {
        // Clear any stale state if no valid cache
        setCachedExtractedData([]);
        setHasExtractedData(false);
        setCacheInfo(null);
      }
    };

    if (hotelId) {
      initializeCache();
    }
  }, [hotelId]);

  // Update cache info periodically to show remaining time
  useEffect(() => {
    if (!hasExtractedData) return;

    const interval = setInterval(() => {
      const updatedCacheInfo = getTariffCacheInfo();
      if (updatedCacheInfo && updatedCacheInfo.isValid) {
        setCacheInfo(updatedCacheInfo);
      } else {
        // Cache expired, clear state
        setCachedExtractedData([]);
        setHasExtractedData(false);
        setCacheInfo(null);
        toast.error('Cached tariff data has expired. Please upload a new PDF.');
      }
    }, 1000); // Update every second

    return () => clearInterval(interval);
  }, [hasExtractedData]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];

      // Check if file is a PDF
      if (file.type !== 'application/pdf') {
        toast.error('Only PDF files are supported');
        return;
      }

      setSelectedFile(file);
    }
  };

  // New function to handle PDF upload and extraction with caching
  const handlePDFUpload = async () => {
    if (!selectedFile) {
      toast.error('Please select a PDF file');
      return;
    }

    // Check if we already have cached data for the same file
    if (isSameFile(selectedFile, hotelId)) {
      const cachedData = getCachedTariffData(hotelId);
      if (cachedData && cachedData.length > 0) {
        setCachedExtractedData(cachedData);
        setHasExtractedData(true);
        const remainingTime = getCacheRemainingTimeFormatted();
        toast.success(`Using cached data for this file (${remainingTime} remaining)`);
        return;
      }
    }

    setIsExtracting(true);

    try {
      // Extract tariff data if service is available
      let extractedTariffData: ExtractedTariffData[] = [];
      if (extractionServiceAvailable) {
        try {
          extractedTariffData = await extractTariffFromPDF(selectedFile, true);

          // Cache the extracted data
          cacheTariffData(extractedTariffData, hotelId, selectedFile);

          // Update component state
          setCachedExtractedData(extractedTariffData);
          setHasExtractedData(true);
          setCacheInfo(getTariffCacheInfo());

          toast.success('PDF data extracted and cached successfully! Now select a room to view relevant prices.');
        } catch (extractionError) {
          console.warn('Failed to extract tariff data:', extractionError);
          toast.error('Could not extract tariff data automatically. Please try again with a different PDF.');
          return;
        }
      } else {
        // Generate mock data for demonstration
        const mockData = generateMockExtractedDataForAllRooms();
        setCachedExtractedData(mockData);
        setHasExtractedData(true);
        toast.success('PDF processed successfully! (Using mock data for demo). Now select a room to view relevant prices.');
      }

    } catch (error) {
      console.error('Error processing PDF:', error);
      if (error instanceof Error) {
        toast.error(`Error processing PDF: ${error.message}`);
      } else {
        toast.error('Failed to process PDF. Please try again.');
      }
    } finally {
      setIsExtracting(false);
    }
  };

  // Function to handle cache refresh
  const handleCacheRefresh = async () => {
    if (!selectedFile) {
      toast.error('Please select a PDF file first');
      return;
    }

    // Clear existing cache
    clearTariffCache();
    setCachedExtractedData([]);
    setHasExtractedData(false);
    setCacheInfo(null);

    // Re-extract data
    await handlePDFUpload();
  };



  // Function to show comparison for selected room
  const handleShowComparison = () => {
    if (!selectedRoom || !hasExtractedData) {
      toast.error('Please select a room and ensure PDF data is extracted');
      return;
    }

    // Filter cached data for selected room
    const roomData = rooms.find(r => r.hotelRoomId === selectedRoom);
    if (!roomData) {
      toast.error('Selected room not found');
      return;
    }

    // Create a mock tariff object for comparison
    const mockTariff: TariffUpload = {
      tariffId: `temp-${Date.now()}`,
      hotelId,
      roomId: selectedRoom,
      filePath: `temp-${selectedFile?.name}`,
      uploadDate: new Date().toISOString(),
      status: 'pending',
      priceData: getFilteredDataForRoom(selectedRoom)
    };

    setTariffForComparison(mockTariff);
    setComparisonOpen(true);
  };

  // Clear cached data
  const handleClearCache = () => {
    // Clear cache from localStorage
    clearTariffCache();

    // Clear component state
    setCachedExtractedData([]);
    setHasExtractedData(false);
    setCacheInfo(null);
    setSelectedRoom('');
    setSelectedFile(null);

    // Clear file input
    const uploadInput = document.querySelector('input[type="file"]') as HTMLInputElement;
    if (uploadInput) uploadInput.value = '';

    toast.success('Cache cleared successfully');
  };

  const handleTariffAction = async (tariffId: string, action: 'approve' | 'reject') => {
    try {
      await updateTariffStatus(
        tariffId,
        action === 'approve' ? 'approved' : 'rejected'
      );
      queryClient.invalidateQueries(['tariffs', hotelId]);

      // Close the details dialog if it's open
      if (tariffDetailsOpen) {
        setTariffDetailsOpen(false);
      }
    } catch (error) {
      console.error(`Error ${action}ing tariff:`, error);
      toast.error(`Failed to ${action} tariff. Please try again.`);
    }
  };

  const handleTariffDelete = async (tariffId: string) => {
    try {
      await deleteTariffUpload(tariffId);
      queryClient.invalidateQueries(['tariffs', hotelId]);

      // Close the details dialog if it's open with the deleted tariff
      if (tariffDetailsOpen && selectedTariff?.tariffId === tariffId) {
        setTariffDetailsOpen(false);
      }

      // Close the comparison view if it's open with the deleted tariff
      if (comparisonOpen && tariffForComparison?.tariffId === tariffId) {
        setComparisonOpen(false);
      }
    } catch (error) {
      console.error('Error deleting tariff:', error);
      toast.error('Failed to delete tariff. Please try again.');
    }
  };

  const showTariffDetails = (tariff: TariffUpload) => {
    setSelectedTariff(tariff);
    setTariffDetailsOpen(true);
  };

  const showComparisonView = (tariff: TariffUpload) => {
    setTariffForComparison(tariff);
    setComparisonOpen(true);
  };

  // Function to get room name by ID
  const getRoomName = (roomId: string) => {
    const room = rooms?.find(r => r.hotelRoomId === roomId);
    const roomName = room ? room.hotelRoomType : 'Unknown Room';
    console.log('TariffUploadList - getRoomName for roomId:', roomId, 'returning:', roomName);
    return roomName;
  };

  // Generate mock data for all rooms (used when extraction service is not available)
  const generateMockExtractedDataForAllRooms = (): ExtractedTariffData[] => {
    const result: ExtractedTariffData[] = [];
    const seasons = ['Peak Season', 'Off Season', 'Mid Season'];
    const mealPlanTypes = ['ep', 'cp', 'map', 'ap'];

    rooms.forEach(room => {
      mealPlanTypes.forEach(type => {
        // Create sample date ranges (next 3 months)
        const today = new Date();
        const startDate1 = new Date(today);
        const endDate1 = new Date(today);
        endDate1.setMonth(endDate1.getMonth() + 1);

        const startDate2 = new Date(endDate1);
        startDate2.setDate(startDate2.getDate() + 1);
        const endDate2 = new Date(startDate2);
        endDate2.setMonth(endDate2.getMonth() + 1);

        // Generate base price
        const basePrice = 3000 + Math.floor(Math.random() * 2000);

        result.push({
          'Season': seasons[Math.floor(Math.random() * seasons.length)],
          'Start Date': startDate1.toISOString().split('T')[0],
          'End Date': endDate1.toISOString().split('T')[0],
          'Room Category': room.hotelRoomType,
          'Plan': type.toUpperCase(),
          'Room Price': basePrice + Math.floor(Math.random() * 500),
          'Adult Price': 0,
          'Child Price': 0,
          'Hotel': 'Sample Hotel',
          'Occupancy': 'Double',
          'Meal Plan': type,
          'Price': basePrice + Math.floor(Math.random() * 500)
        });

        result.push({
          'Season': seasons[Math.floor(Math.random() * seasons.length)],
          'Start Date': startDate2.toISOString().split('T')[0],
          'End Date': endDate2.toISOString().split('T')[0],
          'Room Category': room.hotelRoomType,
          'Plan': type.toUpperCase(),
          'Room Price': basePrice + Math.floor(Math.random() * 800),
          'Adult Price': 0,
          'Child Price': 0,
          'Hotel': 'Sample Hotel',
          'Occupancy': 'Double',
          'Meal Plan': type,
          'Price': basePrice + Math.floor(Math.random() * 800)
        });
      });
    });

    return result;
  };

  // Filter cached data for a specific room
  const getFilteredDataForRoom = (roomId: string): ExtractedTariffData[] => {
    const room = rooms.find(r => r.hotelRoomId === roomId);
    if (!room || !cachedExtractedData.length) return [];

    // Filter data by room category/type using improved fuzzy matching
    return cachedExtractedData.filter(data => {
      const roomCategory = data['Room Category'];
      if (!roomCategory) return true; // Include if no room category specified

      return filterRoomsByName(roomCategory, room.hotelRoomType, 0.6);
    });
  };

  // Mock extracted data for demonstration purposes (kept for backward compatibility)
  const generateMockExtractedData = (): ExtractedTariffData[] => {
    if (!tariffForComparison) return [];
    return getFilteredDataForRoom(tariffForComparison.roomId);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <span className="ml-3 text-gray-600">Loading...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Service Status Alert */}
      <Alert className={extractionServiceAvailable ? "bg-green-50 border-green-200" : "bg-amber-50 border-amber-200"}>
        <Info className={`h-5 w-5 ${extractionServiceAvailable ? 'text-green-600' : 'text-amber-600'}`} />
        <AlertTitle className={extractionServiceAvailable ? "text-green-800" : "text-amber-800"}>
          {extractionServiceAvailable ? 'PDF Extraction Service Active' : 'PDF Extraction Service Unavailable'}
        </AlertTitle>
        <AlertDescription className={extractionServiceAvailable ? "text-green-700" : "text-amber-700"}>
          {extractionServiceAvailable 
            ? 'PDF data will be automatically extracted and compared with existing tariffs when you upload files.'
            : 'PDF extraction service is not running. Mock data will be used for demonstration. To enable extraction, start the backend service at http://localhost:5000'
          }
        </AlertDescription>
      </Alert>

      {/* Upload Form */}
      <div className="bg-white rounded-lg border p-5 shadow-sm">
        <h3 className="text-base font-semibold mb-4 text-gray-800">Process Tariff PDF</h3>

        {/* Step 1: PDF Upload */}
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Step 1: Upload and Extract PDF Data</h4>
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <input
                type="file"
                accept=".pdf"
                onChange={handleFileChange}
                className="block w-full text-sm text-gray-500
                  file:mr-4 file:py-2 file:px-4
                  file:rounded-md file:border-0
                  file:text-sm file:font-medium
                  file:bg-blue-50 file:text-blue-700
                  hover:file:bg-blue-100"
              />
              {selectedFile && (
                <p className="mt-1 text-sm text-gray-500">
                  {selectedFile.name} ({Math.round(selectedFile.size / 1024)} KB)
                </p>
              )}
            </div>
            <Button
              onClick={handlePDFUpload}
              disabled={!selectedFile || isExtracting}
              className="whitespace-nowrap"
            >
              {isExtracting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Extracting...
                </>
              ) : (
                <>
                  <Upload size={16} className="mr-2" />
                  Extract Data
                </>
              )}
            </Button>
            {/* Cache Refresh Button */}
            {hasExtractedData && (
              <Button
                onClick={handleCacheRefresh}
                variant="outline"
                size="sm"
                className="whitespace-nowrap"
              >
                <RefreshCw size={16} className="mr-2" />
                Refresh Cache
              </Button>
            )}
          </div>
        </div>

        {/* Step 2: Room Selection and Comparison */}
        {hasExtractedData && (
          <div className="border-t pt-4">
            <h4 className="text-sm font-medium text-gray-700 mb-2">Step 2: Select Room and View Comparison</h4>
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <select
                  value={selectedRoom}
                  onChange={(e) => setSelectedRoom(e.target.value)}
                  className="w-full rounded-md border border-gray-300 py-2 px-3 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                >
                  <option value="">Select a room to view prices</option>
                  {rooms?.map((room) => (
                    <option key={room.hotelRoomId} value={room.hotelRoomId}>
                      {room.hotelRoomType}
                    </option>
                  ))}
                </select>
              </div>
              <Button
                onClick={handleShowComparison}
                disabled={!selectedRoom}
                className="whitespace-nowrap"
              >
                View Comparison
              </Button>
              <Button
                onClick={handleClearCache}
                variant="outline"
                className="whitespace-nowrap text-red-600 border-red-200 hover:bg-red-50"
              >
                Clear Cache
              </Button>
            </div>
          </div>
        )}

        {/* Status indicator */}
        {hasExtractedData && (
          <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Check className="h-4 w-4 text-green-600 mr-2" />
                <span className="text-sm text-green-800">
                  PDF data extracted and cached ({cachedExtractedData.length} records). Select different rooms to view their specific prices.
                </span>
              </div>
              <div className="text-xs text-green-600">
                Cache expires in: {getCacheRemainingTimeFormatted()}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Error Message */}
      {isError && (
        <Alert variant="destructive" className="mt-2">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            There was an error fetching tariff data. The backend API might not be fully implemented yet.
          </AlertDescription>
        </Alert>
      )}

      {/* Tariff List */}
      <div>
        <h3 className="text-base font-medium mb-3 text-gray-700">Uploaded Tariffs</h3>

        {tariffs.length === 0 ? (
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 text-center text-gray-500">
            No tariff files have been uploaded yet.
          </div>
        ) : (
          <div className="bg-white border rounded-lg overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Room Type
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tariff File
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Upload Date
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {tariffs.map((tariff) => (
                  <tr key={tariff.tariffId} className="hover:bg-gray-50">
                    <td className="px-4 py-3 text-sm text-gray-900">
                      {getRoomName(tariff.roomId)}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900">
                      <a
                        href={tariff.filePath.includes('mock')
                          ? '#'
                          : `https://tripemilestone.in-maa-1.linodeobjects.com/${tariff.filePath}`
                        }
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center text-blue-600 hover:underline"
                        onClick={(e) => {
                          if (tariff.filePath.includes('mock')) {
                            e.preventDefault();
                            toast.success('This is a demo file (not actually uploaded to server)');
                          }
                        }}
                      >
                        <FileText size={16} className="mr-1" />
                        {tariff.filePath.split('-').pop() || 'Demo-Tariff.pdf'}
                        {tariff.filePath.includes('mock') && <span className="ml-1 text-xs text-amber-600">(DEMO)</span>}
                      </a>
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-500">
                      {format(new Date(tariff.uploadDate), 'MMM d, yyyy')}
                    </td>
                    <td className="px-4 py-3 text-sm">
                      {tariff.status === 'pending' && (
                        <span className="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">
                          Pending
                        </span>
                      )}
                      {tariff.status === 'approved' && (
                        <span className="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                          Approved
                        </span>
                      )}
                      {tariff.status === 'rejected' && (
                        <span className="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800">
                          Rejected
                        </span>
                      )}
                    </td>
                    <td className="px-4 py-3 text-sm text-right space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => showTariffDetails(tariff)}
                        className="text-blue-600 border-blue-200 hover:bg-blue-50"
                      >
                        Details
                      </Button>

                      {tariff.status === 'pending' && (
                        <>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => showComparisonView(tariff)}
                            className="text-blue-600 border-blue-200 hover:bg-blue-50"
                          >
                            Compare
                          </Button>

                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleTariffAction(tariff.tariffId!, 'approve')}
                            className="text-green-600 border-green-200 hover:bg-green-50"
                          >
                            <Check size={16} className="mr-1" />
                            Approve
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleTariffAction(tariff.tariffId!, 'reject')}
                            className="text-red-600 border-red-200 hover:bg-red-50"
                          >
                            <X size={16} className="mr-1" />
                            Reject
                          </Button>
                        </>
                      )}

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleTariffDelete(tariff.tariffId!)}
                        className="text-red-600 border-red-200 hover:bg-red-50"
                      >
                        Delete
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Tariff Details Dialog */}
      {selectedTariff && (
        <Dialog open={tariffDetailsOpen} onOpenChange={setTariffDetailsOpen}>
          <DialogContent className="sm:max-w-lg">
            <DialogHeader>
              <DialogTitle>Tariff Details</DialogTitle>
              <DialogDescription>
                Details for {getRoomName(selectedTariff.roomId)}
                {selectedTariff.tariffId?.includes('mock') && <span className="ml-2 text-amber-600 text-xs">(DEMO MODE)</span>}
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4 my-2">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Room</h4>
                  <p className="text-sm">{getRoomName(selectedTariff.roomId)}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Status</h4>
                  <p className="text-sm">
                    {selectedTariff.status === 'pending' && 'Pending Review'}
                    {selectedTariff.status === 'approved' && 'Approved'}
                    {selectedTariff.status === 'rejected' && 'Rejected'}
                  </p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Upload Date</h4>
                  <p className="text-sm">{format(new Date(selectedTariff.uploadDate), 'MMM d, yyyy')}</p>
                </div>
                {selectedTariff.approvalDate && (
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">Approval Date</h4>
                    <p className="text-sm">{format(new Date(selectedTariff.approvalDate), 'MMM d, yyyy')}</p>
                  </div>
                )}
              </div>

              <div>
                <h4 className="text-sm font-medium text-gray-500">Tariff File</h4>
                <a
                  href={selectedTariff.filePath.includes('mock')
                    ? '#'
                    : `https://tripemilestone.in-maa-1.linodeobjects.com/${selectedTariff.filePath}`
                  }
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 text-sm hover:underline flex items-center"
                  onClick={(e) => {
                    if (selectedTariff.filePath.includes('mock')) {
                      e.preventDefault();
                      toast.success('This is a demo file (not actually uploaded to server)');
                    }
                  }}
                >
                  <FileText size={16} className="mr-1" />
                  {selectedTariff.filePath.split('-').pop() || 'Demo-Tariff.pdf'}
                  {selectedTariff.filePath.includes('mock') && <span className="ml-1 text-xs text-amber-600">(DEMO)</span>}
                </a>
              </div>

              {selectedTariff.notes && (
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Notes</h4>
                  <p className="text-sm">{selectedTariff.notes}</p>
                </div>
              )}

              {selectedTariff.priceData && selectedTariff.priceData.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-500 mb-2">Price Data</h4>
                  <div className="border rounded-md overflow-hidden">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-3 py-2 text-left text-xs font-medium text-gray-500">Meal Plan</th>
                          <th className="px-3 py-2 text-left text-xs font-medium text-gray-500">Date Range</th>
                          <th className="px-3 py-2 text-right text-xs font-medium text-gray-500">Price (₹)</th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-gray-200">
                        {selectedTariff.priceData.map((price, index) => (
                          <tr key={index}>
                            <td className="px-3 py-2 text-xs">{price['Plan'] || price['Meal Plan'] || 'N/A'}</td>
                            <td className="px-3 py-2 text-xs">
                              {(() => {
                                try {
                                  const startDate = new Date(price['Start Date']);
                                  const endDate = new Date(price['End Date']);
                                  return `${format(startDate, 'MMM d, yyyy')} - ${format(endDate, 'MMM d, yyyy')}`;
                                } catch (error) {
                                  return `${price['Start Date']} - ${price['End Date']}`;
                                }
                              })()}
                            </td>
                            <td className="px-3 py-2 text-xs text-right">{price['Room Price'] || price['Price'] || 'N/A'}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {selectedTariff.status === 'pending' && (
                <Alert className="mt-4">
                  <Info className="h-4 w-4" />
                  <AlertTitle>Processing Required</AlertTitle>
                  <AlertDescription>
                    This tariff needs to be processed and approved for prices to be updated.
                  </AlertDescription>
                </Alert>
              )}

              {selectedTariff.tariffId?.includes('mock') && (
                <Alert className="mt-4 bg-blue-50 border-blue-200">
                  <Info className="h-4 w-4 text-blue-500" />
                  <AlertTitle className="text-blue-800">Demo Mode</AlertTitle>
                  <AlertDescription className="text-blue-700">
                    This is a demo tariff record. In production, this would be stored in the database.
                  </AlertDescription>
                </Alert>
              )}
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setTariffDetailsOpen(false)}>
                Close
              </Button>

              {selectedTariff.status === 'pending' && (
                <>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setTariffDetailsOpen(false);
                      showComparisonView(selectedTariff);
                    }}
                    className="text-blue-600 border-blue-200 hover:bg-blue-50"
                  >
                    Compare
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      handleTariffAction(selectedTariff.tariffId!, 'approve');
                      setTariffDetailsOpen(false);
                    }}
                    className="text-green-600 border-green-200 hover:bg-green-50"
                  >
                    <Check size={16} className="mr-1" />
                    Approve
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      handleTariffAction(selectedTariff.tariffId!, 'reject');
                      setTariffDetailsOpen(false);
                    }}
                    className="text-red-600 border-red-200 hover:bg-red-50"
                  >
                    <X size={16} className="mr-1" />
                    Reject
                  </Button>
                </>
              )}
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Tariff Comparison Dialog */}
      {tariffForComparison && (
        <Dialog
          open={comparisonOpen}
          onOpenChange={setComparisonOpen}
        >
          <DialogContent className="max-w-7xl w-full">
            <DialogHeader>
              <DialogTitle>Tariff Comparison</DialogTitle>
              <DialogDescription>
                Compare extracted tariff data with existing pricing
              </DialogDescription>
            </DialogHeader>

            <div style={{ maxHeight: '70vh', overflowY: 'auto' }}>
              <TariffComparison
                tariff={tariffForComparison}
                existingData={rooms.find(r => r.hotelRoomId === tariffForComparison.roomId)?.mealPlan || []}
                extractedData={
                  tariffForComparison.priceData || generateMockExtractedData()
                }
                roomName={getRoomName(tariffForComparison.roomId)}
                onApprove={() => {
                  setComparisonOpen(false);
                  queryClient.invalidateQueries(['tariffs', hotelId]);
                  // Also refresh room data to show updated meal plans
                  queryClient.invalidateQueries(['rooms', hotelId]);
                  queryClient.invalidateQueries(['hotel', hotelId]);
                }}
                onReject={() => {
                  setComparisonOpen(false);
                  queryClient.invalidateQueries(['tariffs', hotelId]);
                }}
                onClose={() => setComparisonOpen(false)}
              />
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default TariffUploadList;