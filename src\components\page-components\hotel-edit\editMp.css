/* Minimal CSS for the EditMp component calendar */

/* Custom scrollbar for better UX */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Ensure the calendar wrapper takes full width */
.rdrDateRangePickerWrapper {
  width: 100%;
}

/* Ensure the calendar month takes full width */
.rdrMonth {
  width: 100% !important;
}

/* Style date display items with corresponding colors */
.rdrDateDisplayWrapper {
  margin-bottom: 10px;
}

.rdrDateDisplayItem {
  border-radius: 8px !important;
  border: 2px solid #e5e7eb !important;
  padding: 8px 12px !important;
  margin: 4px !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
}

.rdrDateDisplayItem:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
}

.rdrDateDisplayItem input {
  font-weight: 600 !important;
  text-align: center !important;
  color: inherit !important;
  background: transparent !important;
  border: none !important;
}

/* Dynamic color classes for date display items */
.rdrDateDisplayItem[data-color] {
  border-color: var(--range-color) !important;
  background-color: var(--range-color-light) !important;
  color: var(--range-color) !important;
}

.rdrDateDisplayItem[data-color]:before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: var(--range-color);
  border-radius: 4px 0 0 4px;
}

/* Enhanced calendar layout for maximized mode */
.rdrMonthsHorizontal-maximized {
  width: 100% !important;
  min-height: 400px !important;
}

.rdrMonthsHorizontal-maximized .rdrMonths {
  display: flex !important;
  justify-content: space-between !important;
  gap: 15px !important;
  width: 100% !important;
}

.rdrMonthsHorizontal-maximized.flex-1 {
  flex: 1 !important;
}

.rdrMonthsHorizontal-maximized .rdrMonth {
  flex: 1 !important;
  max-width: calc(50% - 7.5px) !important;
  margin: 0 !important;
}

/* Better spacing for maximized calendar */
.rdrMonthsHorizontal-maximized .rdrWeekDays,
.rdrMonthsHorizontal-maximized .rdrDays {
  width: 100% !important;
}

.rdrMonthsHorizontal-maximized .rdrDay {
  height: 45px !important;
  width: 45px !important;
}

.rdrMonthsHorizontal-maximized .rdrDayNumber {
  font-size: 14px !important;
  line-height: 45px !important;
}

/* Responsive calendar container */
@media (max-width: 1200px) {
  .rdrMonthsHorizontal-maximized .rdrMonth {
    max-width: 100% !important;
  }
  
  .rdrMonthsHorizontal-maximized .rdrMonths {
    flex-direction: column !important;
    gap: 10px !important;
  }
}

/* New styles for better visual feedback */
.updated-row-highlight {
  animation: highlightPulse 3s ease-in-out;
}

@keyframes highlightPulse {
  0% {
    background-color: #f0fdf4;
    border-left-color: #22c55e;
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
  }
  50% {
    background-color: #dcfce7;
    border-left-color: #16a34a;
    box-shadow: 0 0 0 10px rgba(34, 197, 94, 0);
  }
  100% {
    background-color: #f9fafb;
    border-left-color: transparent;
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
  }
}

/* Edit History Styles */
.edit-history-primary {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 50%, #eff6ff 100%);
  border-left: 4px solid #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
}

.edit-history-secondary {
  background: linear-gradient(135deg, #faf5ff 0%, #e9d5ff 50%, #faf5ff 100%);
  border-left: 4px solid #8b5cf6;
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.15);
}

.edit-history-tertiary {
  background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 50%, #fff7ed 100%);
  border-left: 4px solid #f97316;
  box-shadow: 0 2px 8px rgba(249, 115, 22, 0.15);
}

.edit-count-badge {
  animation: badgePulse 2s ease-in-out infinite;
}

@keyframes badgePulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.edit-history-tooltip {
  backdrop-filter: blur(8px);
  background: rgba(31, 41, 55, 0.95);
  border: 1px solid rgba(75, 85, 99, 0.3);
}

/* Hover effects for highlighted rows */
.meal-plan-item-highlighted:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease-in-out;
}

.success-toast-enter {
  transform: translateX(100%);
  opacity: 0;
}

.success-toast-enter-active {
  transform: translateX(0);
  opacity: 1;
  transition: all 300ms ease-out;
}

.success-toast-exit {
  transform: translateX(0);
  opacity: 1;
}

.success-toast-exit-active {
  transform: translateX(100%);
  opacity: 0;
  transition: all 300ms ease-in;
}

/* Loading spinner for refresh button */
.refresh-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Enhanced button hover effects */
.btn-enhanced {
  transition: all 0.2s ease-in-out;
}

.btn-enhanced:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-enhanced:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}
