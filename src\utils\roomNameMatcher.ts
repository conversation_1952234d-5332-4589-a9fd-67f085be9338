import stringSimilarity from 'string-similarity';

/**
 * Utility functions for matching room names between hotel database and PDF extraction
 * Handles variations in room names like missing words, different spellings, etc.
 */

/**
 * Normalize room name for comparison
 * - Convert to lowercase
 * - Remove extra spaces
 * - Remove common words that might vary
 * - Remove special characters
 * - Handle common variations
 */
export const normalizeRoomName = (name: string): string => {
  if (!name) return '';

  return name
    .toLowerCase()
    .replace(/\s+/g, ' ') // Replace multiple spaces with single space
    .replace(/[^\w\s]/g, ' ') // Replace special characters with space
    .replace(/\b(room|type|category|suite)\b/g, '') // Remove common room type words but keep descriptive words like deluxe, premium
    .replace(/\bexotica\b/g, 'exotic') // Handle common variations
    .replace(/\bbalcony\b/g, 'balcony') // Normalize balcony spelling
    .replace(/\bmountain\s+view\b/g, 'mountainview') // Normalize mountain view
    .replace(/\bgarden\s+view\b/g, 'gardenview') // Normalize garden view
    .replace(/\bnon\s+view\b/g, 'nonview') // Normalize non view
    .replace(/\bfirst\s+floor\b/g, 'firstfloor') // Normalize first floor
    .replace(/\s+/g, ' ') // Clean up multiple spaces again
    .trim();
};

/**
 * Extract key words from room name for keyword matching
 */
export const extractKeywords = (name: string): string[] => {
  const normalized = normalizeRoomName(name);
  return normalized.split(' ').filter(word => word.length > 2); // Filter out short words
};

/**
 * Calculate similarity score between two room names using multiple methods
 */
export const calculateRoomNameSimilarity = (name1: string, name2: string): number => {
  if (!name1 || !name2) return 0;

  const norm1 = normalizeRoomName(name1);
  const norm2 = normalizeRoomName(name2);

  // Method 1: Direct string similarity
  const directSimilarity = stringSimilarity.compareTwoStrings(norm1, norm2);

  // Method 2: Keyword matching with improved logic
  const keywords1 = extractKeywords(name1);
  const keywords2 = extractKeywords(name2);

  let keywordMatches = 0;
  const totalUniqueKeywords = new Set([...keywords1, ...keywords2]).size;

  if (totalUniqueKeywords > 0) {
    // Count matches from both directions but avoid double counting
    const matchedKeywords = new Set<string>();

    keywords1.forEach(keyword1 => {
      const bestMatch = stringSimilarity.findBestMatch(keyword1, keywords2);
      if (bestMatch.bestMatch.rating > 0.7) {
        matchedKeywords.add(keyword1);
        matchedKeywords.add(bestMatch.bestMatch.target);
      }
    });

    keywordMatches = matchedKeywords.size / 2; // Divide by 2 since we added pairs
  }

  const keywordSimilarity = totalUniqueKeywords > 0 ? keywordMatches / Math.max(keywords1.length, keywords2.length) : 0;

  // Method 3: Enhanced substring matching
  let substringMatch = 0;
  if (norm1.includes(norm2) || norm2.includes(norm1)) {
    substringMatch = 0.8;
  } else {
    // Check if most words from shorter name are in longer name
    const shorter = norm1.length < norm2.length ? norm1 : norm2;
    const longer = norm1.length < norm2.length ? norm2 : norm1;
    const shorterWords = shorter.split(' ').filter(w => w.length > 2);
    const longerWords = longer.split(' ').filter(w => w.length > 2);

    if (shorterWords.length > 0) {
      const matchingWords = shorterWords.filter(word =>
        longerWords.some(longerWord =>
          longerWord.includes(word) || word.includes(longerWord) ||
          stringSimilarity.compareTwoStrings(word, longerWord) > 0.8
        )
      );

      if (matchingWords.length / shorterWords.length >= 0.7) {
        substringMatch = 0.6;
      }
    }
  }

  // Method 4: Special case handling for common room type patterns
  let specialCaseBonus = 0;
  const specialPatterns = [
    ['studio', 'garden', 'view', 'balcony'],
    ['deluxe', 'nonview'],
    ['premium', 'firstfloor', 'balcony', 'mountainview'],
    ['exotic', 'firstfloor', 'balcony', 'mountainview'],
    ['family', 'nonview'],
    ['quad', 'occupancy']
  ];

  for (const pattern of specialPatterns) {
    const pattern1Match = pattern.every(word => norm1.includes(word));
    const pattern2Match = pattern.every(word => norm2.includes(word));
    if (pattern1Match && pattern2Match) {
      specialCaseBonus = 0.3;
      break;
    }
  }

  // Combine all methods with weights
  const finalScore = (directSimilarity * 0.3) + (keywordSimilarity * 0.3) + (substringMatch * 0.2) + (specialCaseBonus * 0.2);

  return Math.min(finalScore, 1.0); // Ensure score doesn't exceed 1.0
};

/**
 * Find the best matching room name from a list of candidates
 */
export const findBestRoomMatch = (
  targetRoomName: string,
  candidateRooms: Array<{ hotelRoomId: string; hotelRoomType: string }>,
  threshold: number = 0.6
): { room: { hotelRoomId: string; hotelRoomType: string } | null; score: number } => {
  if (!targetRoomName || !candidateRooms || candidateRooms.length === 0) {
    return { room: null, score: 0 };
  }
  
  let bestMatch = { room: null as any, score: 0 };
  
  candidateRooms.forEach(room => {
    const score = calculateRoomNameSimilarity(targetRoomName, room.hotelRoomType);
    if (score > bestMatch.score && score >= threshold) {
      bestMatch = { room, score };
    }
  });
  
  return bestMatch;
};

/**
 * Enhanced room filtering function that uses fuzzy matching
 */
export const filterRoomsByName = (
  extractedRoomName: string,
  selectedRoomName: string,
  threshold: number = 0.6 // Increased threshold to be more strict
): boolean => {
  if (!extractedRoomName || !selectedRoomName) return true; // Include if no room category specified

  const similarity = calculateRoomNameSimilarity(extractedRoomName, selectedRoomName);

  // Enhanced simple matching with better normalization
  const normalizedExtracted = normalizeRoomName(extractedRoomName);
  const normalizedSelected = normalizeRoomName(selectedRoomName);

  // Check for exact match after normalization
  const exactMatch = normalizedExtracted === normalizedSelected;

  // Check for substring match - but be more strict to avoid false positives
  let substringMatch = false;
  if (normalizedExtracted === normalizedSelected) {
    substringMatch = true;
  } else if (normalizedExtracted.includes(normalizedSelected) || normalizedSelected.includes(normalizedExtracted)) {
    // Only allow substring match if the difference is minimal (no significant extra words)
    const longer = normalizedExtracted.length > normalizedSelected.length ? normalizedExtracted : normalizedSelected;
    const shorter = normalizedExtracted.length > normalizedSelected.length ? normalizedSelected : normalizedExtracted;
    const longerWords = longer.split(' ').filter(w => w.length > 2);
    const shorterWords = shorter.split(' ').filter(w => w.length > 2);

    // Only allow if the longer name doesn't have significant extra descriptive words
    const extraWords = longerWords.filter(word =>
      !shorterWords.some(shorterWord =>
        word.includes(shorterWord) || shorterWord.includes(word) ||
        stringSimilarity.compareTwoStrings(word, shorterWord) > 0.8
      )
    );

    // Filter out common connecting words
    const significantExtraWords = extraWords.filter(word =>
      !['with', 'and', 'the', 'of', 'in', 'on', 'at', 'to', 'for', 'by'].includes(word)
    );

    substringMatch = significantExtraWords.length === 0;
  }

  // Check if most significant words match
  const extractedWords = normalizedExtracted.split(' ').filter(w => w.length > 2);
  const selectedWords = normalizedSelected.split(' ').filter(w => w.length > 2);

  let significantWordMatch = false;
  if (extractedWords.length > 0 && selectedWords.length > 0) {
    const matchingWords = extractedWords.filter(word =>
      selectedWords.some(selectedWord =>
        word.includes(selectedWord) || selectedWord.includes(word) ||
        stringSimilarity.compareTwoStrings(word, selectedWord) > 0.8
      )
    );

    // Check for unique words that don't match (like "triangle" in "Deluxe Triangle Room Non View")
    const uniqueWords = extractedWords.filter(word =>
      !selectedWords.some(selectedWord =>
        word.includes(selectedWord) || selectedWord.includes(word) ||
        stringSimilarity.compareTwoStrings(word, selectedWord) > 0.8
      )
    );

    // If there are significant unique words that suggest a different room type, don't match
    const significantUniqueWords = uniqueWords.filter(word =>
      !['with', 'and', 'the', 'of', 'in', 'on', 'at', 'to', 'for', 'by'].includes(word)
    );

    // If at least 70% of words from the shorter name match AND no significant unique words
    const minWords = Math.min(extractedWords.length, selectedWords.length);
    significantWordMatch = matchingWords.length >= Math.ceil(minWords * 0.7) && significantUniqueWords.length === 0;
  }

  // Additional check: if one name has specific room type modifiers that the other doesn't, don't match
  const roomTypeModifiers = ['triangle', 'circular', 'square', 'corner', 'penthouse', 'villa', 'cottage', 'cabin'];
  const extractedModifiers = roomTypeModifiers.filter(modifier => normalizedExtracted.includes(modifier));
  const selectedModifiers = roomTypeModifiers.filter(modifier => normalizedSelected.includes(modifier));

  // If one has modifiers and the other doesn't, or they have different modifiers, don't match
  const hasConflictingModifiers = (extractedModifiers.length > 0 && selectedModifiers.length === 0) ||
                                  (extractedModifiers.length === 0 && selectedModifiers.length > 0) ||
                                  (extractedModifiers.length > 0 && selectedModifiers.length > 0 &&
                                   !extractedModifiers.every(mod => selectedModifiers.includes(mod)));

  const result = (similarity >= threshold || exactMatch || substringMatch || significantWordMatch) && !hasConflictingModifiers;

  // Debug logging to help troubleshoot matching issues
  if (process.env.NODE_ENV === 'development') {
    console.log(`Room matching: "${extractedRoomName}" vs "${selectedRoomName}"`, {
      similarity: similarity.toFixed(3),
      exactMatch,
      substringMatch,
      significantWordMatch,
      hasConflictingModifiers,
      extractedModifiers,
      selectedModifiers,
      result,
      normalizedExtracted,
      normalizedSelected
    });
  }

  return result;
};
