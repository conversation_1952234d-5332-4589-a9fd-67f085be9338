import tariffApi from './tariff-api';
import toast from 'react-hot-toast';
import { TariffUpload, TariffPriceData, ExtractedTariffData } from '@/types/types';

// Fetch all tariff uploads for a hotel
export async function fetchHotelTariffs(hotelId: string) {
  try {
    const response = await tariffApi.get(`admin/hotel/${hotelId}/tariffs`);
    return Promise.resolve(response.data.result);
  } catch (error) {
    console.error('Error fetching tariff data:', error);
    toast.error('Error fetching tariff data');
    // Return empty array instead of rejecting to prevent UI from breaking
    return Promise.resolve([]);
  }
}

// Create a new tariff upload
export async function createTariffUpload(tariffData: Omit<TariffUpload, 'tariffId' | 'uploadDate' | 'status'>) {
  try {
    const response = await tariffApi.post('admin/hotel/tariff', tariffData);
    toast.success('Tariff uploaded successfully');
    return Promise.resolve(response.data.result);
  } catch (error) {
    console.error('Error uploading tariff:', error);
    toast.error('Error uploading tariff');
    return Promise.reject(error);
  }
}

// Update tariff status (approve/reject)
export async function updateTariffStatus(
  tariffId: string,
  status: 'approved' | 'rejected',
  priceData?: TariffPriceData[] | ExtractedTariffData[],
  notes?: string
) {
  try {
    const response = await tariffApi.put(`admin/hotel/tariff/${tariffId}`, {
      status,
      priceData,
      notes
    });

    const actionText = status === 'approved' ? 'approved' : 'rejected';
    toast.success(`Tariff ${actionText} successfully`);
    return Promise.resolve(response.data.result);
  } catch (error) {
    console.error(`Error ${status === 'approved' ? 'approving' : 'rejecting'} tariff:`, error);
    toast.error(`Error ${status === 'approved' ? 'approving' : 'rejecting'} tariff`);
    return Promise.reject(error);
  }
}

// Delete a tariff upload
export async function deleteTariffUpload(tariffId: string) {
  try {
    const response = await tariffApi.delete(`admin/hotel/tariff/${tariffId}`);
    toast.success('Tariff deleted successfully');
    return Promise.resolve(response.data.result);
  } catch (error) {
    console.error('Error deleting tariff:', error);
    toast.error('Error deleting tariff');
    return Promise.reject(error);
  }
}