import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { 
  Eye, 
  EyeOff, 
  Clock, 
  Info,
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import { useEditHistory } from './EditHistoryManager';
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useQuery } from 'react-query';
import { fetchAllRooms } from '@/utils/api-functions/fetch-rooms';
import { HotelRoom } from '@/types/types';

interface EditHistoryControlsProps {
  className?: string;
  hotelId?: string;
}

const EditHistoryControls: React.FC<EditHistoryControlsProps> = ({ className, hotelId }) => {
  const { editHistory, clearAllHighlights } = useEditHistory();
  const [showDetails, setShowDetails] = useState(false);
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  // Fetch rooms data to get meal plan information
  const { data: rooms } = useQuery<HotelRoom[]>(
    ["rooms", hotelId],
    () => fetchAllRooms(hotelId!),
    {
      enabled: Boolean(hotelId),
      staleTime: 1000 * 60 * 5, // 5 minutes
    }
  );

  // Helper function to get meal plan display name
  const getMealPlanDisplayName = (mealPlanId: string) => {
    if (!rooms) return mealPlanId.slice(-8);
    
    for (const room of rooms) {
      if (room.mealPlan) {
        const mealPlan = room.mealPlan.find(mp => mp.hotelMealId === mealPlanId);
        if (mealPlan) {
          return `${room.hotelRoomType} - ${mealPlan.mealPlan}`;
        }
      }
    }
    return mealPlanId.slice(-8);
  };

  // Helper function to format date ranges for display
  const formatDateRanges = (startDates: string[], endDates: string[]) => {
    if (!startDates || !endDates || startDates.length === 0) return 'None';
    
    if (startDates.length === 1) {
      const startDate = new Date(startDates[0]);
      const endDate = new Date(endDates[0]);
      return `${startDate.toLocaleDateString('en-IN', { day: 'numeric', month: 'short', year: 'numeric' })} - ${endDate.toLocaleDateString('en-IN', { day: 'numeric', month: 'short', year: 'numeric' })}`;
    }
    
    // For multiple ranges, show a compact summary
    const rangeCount = startDates.length;
    if (rangeCount <= 3) {
      // Show all ranges for 3 or fewer
      return startDates.map((start, index) => {
        const startDate = new Date(start);
        const endDate = new Date(endDates[index]);
        return `${startDate.toLocaleDateString('en-IN', { day: 'numeric', month: 'short' })} - ${endDate.toLocaleDateString('en-IN', { day: 'numeric', month: 'short', year: 'numeric' })}`;
      }).join(', ');
    } else {
      // Show first and last range for more than 3
      const firstStart = new Date(startDates[0]);
      const firstEnd = new Date(endDates[0]);
      const lastStart = new Date(startDates[startDates.length - 1]);
      const lastEnd = new Date(endDates[endDates.length - 1]);
      
      return `${firstStart.toLocaleDateString('en-IN', { day: 'numeric', month: 'short' })} - ${firstEnd.toLocaleDateString('en-IN', { day: 'numeric', month: 'short', year: 'numeric' })}, ... ${rangeCount - 2} more, ${lastStart.toLocaleDateString('en-IN', { day: 'numeric', month: 'short' })} - ${lastEnd.toLocaleDateString('en-IN', { day: 'numeric', month: 'short', year: 'numeric' })}`;
    }
  };

  // Helper function to create a detailed table view of date ranges
  const renderDateRangesTable = (
    oldStartDates: string[] = [],
    oldEndDates: string[] = [],
    newStartDates: string[] = [],
    newEndDates: string[] = [],
    oldPrices?: any,
    newPrices?: any
  ) => {
    // Find the max number of ranges
    const maxRows = Math.max(
      oldStartDates?.length || 0,
      newStartDates?.length || 0
    );
    if (maxRows === 0) return null;

    return (
      <div className="mt-2">
        <div className="text-xs text-gray-500 mb-1">Date Ranges:</div>
        <div className="overflow-x-auto">
          <table className="min-w-full text-xs border border-gray-200 rounded">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-2 py-1 text-left border-r" colSpan={3}>Old</th>
                <th className="px-2 py-1 text-left border-r" colSpan={3}>New</th>
              </tr>
              <tr>
                <th className="px-2 py-1 text-left border-r">Start Date</th>
                <th className="px-2 py-1 text-left border-r">End Date</th>
                <th className="px-2 py-1 text-left border-r">Room/Adult/Child</th>
                <th className="px-2 py-1 text-left border-r">Start Date</th>
                <th className="px-2 py-1 text-left border-r">End Date</th>
                <th className="px-2 py-1 text-left">Room/Adult/Child</th>
              </tr>
            </thead>
            <tbody>
              {Array.from({ length: maxRows }).map((_, idx) => {
                const oldStart = oldStartDates[idx];
                const oldEnd = oldEndDates[idx];
                const newStart = newStartDates[idx];
                const newEnd = newEndDates[idx];
                return (
                  <tr key={idx} className="border-t">
                    {/* Old */}
                    <td className="px-2 py-1 border-r">
                      {oldStart ? new Date(oldStart).toLocaleDateString('en-IN', { day: 'numeric', month: 'short', year: 'numeric' }) : <span className="text-gray-400">-</span>}
                    </td>
                    <td className="px-2 py-1 border-r">
                      {oldEnd ? new Date(oldEnd).toLocaleDateString('en-IN', { day: 'numeric', month: 'short', year: 'numeric' }) : <span className="text-gray-400">-</span>}
                    </td>
                    <td className="px-2 py-1 border-r">
                      {oldPrices ? (
                        <div>
                          <div className="flex items-center gap-1">
                            <span className="text-red-600 line-through">₹{oldPrices.roomPrice}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <span className="text-red-600 line-through">₹{oldPrices.adultPrice}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <span className="text-red-600 line-through">₹{oldPrices.childPrice}</span>
                          </div>
                        </div>
                      ) : <span className="text-gray-400">-</span>}
                    </td>
                    {/* New */}
                    <td className="px-2 py-1 border-r">
                      {newStart ? new Date(newStart).toLocaleDateString('en-IN', { day: 'numeric', month: 'short', year: 'numeric' }) : <span className="text-gray-400">-</span>}
                    </td>
                    <td className="px-2 py-1 border-r">
                      {newEnd ? new Date(newEnd).toLocaleDateString('en-IN', { day: 'numeric', month: 'short', year: 'numeric' }) : <span className="text-gray-400">-</span>}
                    </td>
                    <td className="px-2 py-1">
                      {newPrices ? (
                        <div>
                          <div className="flex items-center gap-1">
                            <span className="text-green-600 font-medium">₹{newPrices.roomPrice}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <span className="text-green-600 font-medium">₹{newPrices.adultPrice}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <span className="text-green-600 font-medium">₹{newPrices.childPrice}</span>
                          </div>
                        </div>
                      ) : <span className="text-gray-400">-</span>}
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  // Helper functions for managing expanded state
  const toggleExpanded = (mealPlanId: string) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(mealPlanId)) {
        newSet.delete(mealPlanId);
      } else {
        newSet.add(mealPlanId);
      }
      return newSet;
    });
  };

  const isExpanded = (mealPlanId: string) => expandedItems.has(mealPlanId);

  // Calculate statistics
  const stats = Object.values(editHistory).reduce((acc, item) => {
    acc.totalEdited += 1;
    acc.totalEdits += item.editCount;
    if (item.isHighlighted) acc.currentlyHighlighted += 1;
    return acc;
  }, { totalEdited: 0, totalEdits: 0, currentlyHighlighted: 0 });

  // Get recently edited items (last 24 hours)
  const recentlyEdited = Object.entries(editHistory)
    .filter(([_, item]) => {
      const hoursSinceEdit = (Date.now() - item.lastEditTime) / (1000 * 60 * 60);
      return hoursSinceEdit < 24;
    })
    .sort(([_, a], [__, b]) => b.lastEditTime - a.lastEditTime)
    .slice(0, 5);

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {/* Statistics Badge */}
      {stats.currentlyHighlighted > 0 && (
        <div className="flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
          <Eye className="w-3 h-3" />
          {stats.currentlyHighlighted} highlighted
        </div>
      )}

      {/* Clear All Highlights Button */}
      {stats.currentlyHighlighted > 0 && (
        <Button
          variant="outline"
          size="sm"
          onClick={clearAllHighlights}
          className="text-gray-600 border-gray-200 hover:bg-gray-50"
          title="Clear all highlights"
        >
          <EyeOff className="w-3 h-3 mr-1" />
          Clear
        </Button>
      )}

      {/* History Details Dialog */}
      <Dialog open={showDetails} onOpenChange={setShowDetails}>
        <DialogTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="text-gray-600 border-gray-200 hover:bg-gray-50"
            title="View edit history"
          >
            <Clock className="w-3 h-3 mr-1" />
            History
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-2xl max-h-[80vh]" aria-describedby="edit-history-description">
          <DialogTitle className="flex items-center gap-2">
            <Clock className="w-4 h-4" />
            Edit History
          </DialogTitle>
          <div id="edit-history-description" className="sr-only">
            View and manage edit history for meal plans
          </div>
          
          <div className="space-y-4">
            {/* Statistics */}
            <div className="grid grid-cols-3 gap-4 p-3 bg-gray-50 rounded-lg">
              <div className="text-center">
                <div className="text-lg font-bold text-blue-600">{stats.totalEdited}</div>
                <div className="text-xs text-gray-600">Items Edited</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-purple-600">{stats.totalEdits}</div>
                <div className="text-xs text-gray-600">Total Edits</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-orange-600">{stats.currentlyHighlighted}</div>
                <div className="text-xs text-gray-600">Highlighted</div>
              </div>
            </div>

            {/* Recently Edited with Scrollable Area */}
            {recentlyEdited.length > 0 && (
              <div>
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900 flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    Recently Edited (24h)
                  </h4>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setExpandedItems(new Set(recentlyEdited.map(([id]) => id)))}
                      className="text-xs px-2 py-1 h-6"
                    >
                      Expand All
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setExpandedItems(new Set())}
                      className="text-xs px-2 py-1 h-6"
                    >
                      Collapse All
                    </Button>
                  </div>
                </div>
                <div className="max-h-96 overflow-y-auto space-y-2 pr-2" style={{
                  scrollbarWidth: 'thin',
                  scrollbarColor: '#CBD5E0 #F7FAFC'
                }}>
                  {recentlyEdited.map(([mealPlanId, item]) => {
                    const hoursAgo = Math.floor((Date.now() - item.lastEditTime) / (1000 * 60 * 60));
                    const timeText = hoursAgo === 0 ? 'Just now' : `${hoursAgo}h ago`;
                    const displayName = getMealPlanDisplayName(mealPlanId);
                    const latestChange = item.changes && item.changes.length > 0 ? item.changes[item.changes.length - 1] : null;
                    const expanded = isExpanded(mealPlanId);
                    
                    return (
                      <div key={mealPlanId} className="bg-gray-50 rounded-lg text-sm hover:bg-gray-100 transition-colors">
                        {/* Compact Header */}
                        <div 
                          className="p-3 cursor-pointer flex items-center justify-between"
                          onClick={() => toggleExpanded(mealPlanId)}
                        >
                          <div className="flex items-center gap-3">
                            <div className={`
                              w-3 h-3 rounded-full flex-shrink-0
                              ${item.editCount === 1 ? 'bg-blue-500' : ''}
                              ${item.editCount === 2 ? 'bg-purple-500' : ''}
                              ${item.editCount >= 3 ? 'bg-orange-500' : ''}
                            `} />
                            <div className="flex items-center gap-2">
                              {expanded ? (
                                <ChevronDown className="w-4 h-4 text-gray-500" />
                              ) : (
                                <ChevronRight className="w-4 h-4 text-gray-500" />
                              )}
                              <div className="flex flex-col">
                                <span className="font-medium text-gray-900">{displayName}</span>
                                <span className="text-xs text-gray-500">ID: {mealPlanId.slice(-8)}</span>
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center gap-2 text-xs text-gray-600">
                            <span className="px-2 py-1 bg-white rounded-full border">{item.editCount} edits</span>
                            <span>•</span>
                            <span>{timeText}</span>
                            {!expanded && latestChange && (
                              <span className="text-blue-600">• Click to view changes</span>
                            )}
                          </div>
                        </div>
                        
                        {/* Expandable Change Details */}
                        {expanded && latestChange && (
                          <div className="px-3 pb-3">
                            <div className="p-2 bg-white rounded border-l-4 border-blue-500">
                              <div className="text-xs text-gray-600 mb-2 font-medium">Latest Changes:</div>
                              
                                                          {/* Date Range Changes */}
                            {latestChange.oldStartDates && latestChange.newStartDates && (
                              <div className="mb-2">
                                <div className="text-xs text-gray-500 mb-1">Date Ranges:</div>
                                <div className="flex items-center gap-2 text-xs">
                                  <span className="text-red-600 line-through">
                                    {formatDateRanges(latestChange.oldStartDates, latestChange.oldEndDates || [])}
                                  </span>
                                  <span className="text-gray-400">→</span>
                                  <span className="text-green-600 font-medium">
                                    {formatDateRanges(latestChange.newStartDates, latestChange.newEndDates || [])}
                                  </span>
                                </div>
                                                            </div>
                            )}
                            
                            {/* Detailed Table View for Multiple Ranges */}
                            {expanded && latestChange.oldStartDates && latestChange.newStartDates && 
                             latestChange.oldStartDates.length > 1 && (
                              <div className="mt-3">
                                {renderDateRangesTable(
                                  latestChange.oldStartDates, 
                                  latestChange.oldEndDates || [], 
                                  latestChange.newStartDates, 
                                  latestChange.newEndDates || [],
                                  latestChange.oldPrices, 
                                  latestChange.newPrices
                                )}
                              </div>
                            )}
                            
                            {/* Price Changes */}
                            {latestChange.oldPrices && latestChange.newPrices && (
                                <div>
                                  <div className="text-xs text-gray-500 mb-1">Prices:</div>
                                  <div className="grid grid-cols-3 gap-2 text-xs">
                                    <div>
                                      <span className="text-gray-500">Room:</span>
                                      <div className="flex items-center gap-1">
                                        <span className="text-red-600 line-through">₹{latestChange.oldPrices.roomPrice}</span>
                                        <span className="text-gray-400">→</span>
                                        <span className="text-green-600 font-medium">₹{latestChange.newPrices.roomPrice}</span>
                                      </div>
                                    </div>
                                    {latestChange.oldPrices.adultPrice && latestChange.newPrices.adultPrice && (
                                      <div>
                                        <span className="text-gray-500">Adult:</span>
                                        <div className="flex items-center gap-1">
                                          <span className="text-red-600 line-through">₹{latestChange.oldPrices.adultPrice}</span>
                                          <span className="text-gray-400">→</span>
                                          <span className="text-green-600 font-medium">₹{latestChange.newPrices.adultPrice}</span>
                                        </div>
                                      </div>
                                    )}
                                    {latestChange.oldPrices.childPrice && latestChange.newPrices.childPrice && (
                                      <div>
                                        <span className="text-gray-500">Child:</span>
                                        <div className="flex items-center gap-1">
                                          <span className="text-red-600 line-through">₹{latestChange.oldPrices.childPrice}</span>
                                          <span className="text-gray-400">→</span>
                                          <span className="text-green-600 font-medium">₹{latestChange.newPrices.childPrice}</span>
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Legend */}
            <div className="border-t pt-3">
              <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-1">
                <Info className="w-3 h-3" />
                Color Legend
              </h4>
              <div className="space-y-1 text-sm">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-blue-500 rounded"></div>
                  <span>Primary (1st edit)</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-purple-500 rounded"></div>
                  <span>Secondary (2nd edit)</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-orange-500 rounded"></div>
                  <span>Tertiary (3+ edits)</span>
                </div>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default EditHistoryControls; 