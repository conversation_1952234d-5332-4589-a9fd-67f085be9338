/* eslint-disable @typescript-eslint/no-explicit-any */
import { ReactElement, useContext, useState } from "react";
import AddMealPlan from "./AddMealPlan";
import { useParams } from "react-router-dom";
import { handleMealPlanApi } from "@/utils/api-functions/addMealPlan";
import AppContext from "@/utils/context/AppContext";
import { useQueryClient } from "react-query";
import toast from "react-hot-toast";

export default function AddMealPlans() {
  const queryClient = useQueryClient();
  const {id2 }= useParams();
  const {mealPlanData}:any = useContext(AppContext)
  const [addedRooms, setAddedRooms] = useState<ReactElement[]>([
    <AddMealPlan
    />,
  ]);
  const [isDisabled,setDisabled] = useState(false);
  function handleAddRoooms() {
    setAddedRooms([
      <AddMealPlan
   
      />,
      ...addedRooms,
    ]);
  }
  async function handleSubmitData() {
    setDisabled(true)
    try {
      const loadingToast = toast.loading("Adding meal plans...");
      const resp = await handleMealPlanApi(id2 as string,mealPlanData)
      
      // Dismiss loading toast
      toast.dismiss(loadingToast);
      
      if (resp.data.success) {
        // Invalidate and refetch all related queries
        await queryClient.invalidateQueries(['rooms', id2]);
        await queryClient.invalidateQueries(['hotel', id2]);
        
        toast.success("Meal plans added successfully!", {
          duration: 3000,
          icon: '🎉',
          style: {
            borderRadius: '10px',
            background: '#E8F5E9',
            color: '#1B5E20',
          },
        });
        
        // Use window.location.reload() as fallback for auto-refresh
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      } else {
        toast.error(resp.data.message || "Failed to add meal plans", {
          duration: 4000,
          style: {
            borderRadius: '10px',
            background: '#FFEBEE',
            color: '#B71C1C',
          },
        });
        setDisabled(false);
      }
    } catch (error) {
      toast.error("Failed to add meal plans. Please try again later.", {
        duration: 4000,
        style: {
          borderRadius: '10px',
          background: '#FFEBEE',
          color: '#B71C1C',
        },
      });
      setDisabled(false)
    }
  }
 
  return (
    <div>
      {addedRooms?.map((k, i) => (
        <div key={i}>{k}</div>
      ))}
      <button
        className="bg-yellow-500  px-4 py-2 rounded-lg border shadow m-2"
        onClick={handleAddRoooms}
      >
        add Meal Plan
      </button>

      {mealPlanData?.length > 0 ? (
        <button
          onClick={handleSubmitData}
          className={!isDisabled?"bg-green-500  px-4 py-2 rounded-lg border shadow m-2":"bg-green-500 opacity-75 cursor-not-allowed  px-4 py-2 rounded-lg border shadow m-2"}
          disabled={isDisabled}
        >
          Submit
        </button>
      ) : (
        <></>
      )}
    </div>
  );
}
